import React, { useState, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient, isApiError } from '@/lib/api-client';
import { toast } from '@/hooks/use-toast';
import { handleTokenExpiration, handleUnauthorizedAccess, isTokenExpiredError } from '@/lib/token-expiration-handler';

interface UseApiOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

interface ApiCall<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: (...args: any[]) => Promise<T | void>;
  reset: () => void;
}

// Generic hook for API calls
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
): ApiCall<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const {
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = 'Operation completed successfully',
    onSuccess,
    onError,
  } = options;

  const execute = useCallback(async (...args: any[]): Promise<T | void> => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      setLoading(true);
      setError(null);

      const result = await apiFunction(...args);
      setData(result);

      if (showSuccessToast) {
        toast({
          title: "Success",
          description: successMessage,
        });
      }

      if (onSuccess) onSuccess(result);
      return result;

    } catch (error) {
      console.error('API call error:', error);

      let errorMessage = 'An unexpected error occurred';

      if (isApiError(error)) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if (error.status === 403) {
          errorMessage = 'You do not have permission to perform this action.';
        } else if (error.status === 404) {
          errorMessage = 'The requested resource was not found.';
        } else if (error.status === 422) {
          errorMessage = 'Invalid data provided. Please check your input.';
        } else if (error.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);

      if (showErrorToast) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }

      if (onError) onError(error);
      throw error;

    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [apiFunction, showSuccessToast, showErrorToast, successMessage, onSuccess, onError]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
}

// Specialized hooks for common operations

// Hook for GET operations that auto-execute
export function useApiQuery<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  args: any[] = [],
  options: UseApiOptions & { enabled?: boolean } = {}
) {
  const { enabled = true, ...apiOptions } = options;
  const apiCall = useApi(apiFunction, { ...apiOptions, showErrorToast: false });

  // Auto-execute on mount if enabled
  React.useEffect(() => {
    if (enabled) {
      apiCall.execute(...args);
    }
  }, [enabled, ...args]);

  return apiCall;
}

// Hook for mutations (POST, PUT, DELETE operations)
export function useApiMutation<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
) {
  return useApi(apiFunction, {
    showSuccessToast: true,
    ...options,
  });
}

// Specific hooks for listings operations
export function useListingsApi() {
  const getListings = useApi(apiClient.getListings.bind(apiClient));
  const getListing = useApi(apiClient.getListing.bind(apiClient));
  const createListing = useApiMutation(apiClient.createListing.bind(apiClient), {
    successMessage: 'Listing created successfully',
  });
  const updateListing = useApiMutation(apiClient.updateListing.bind(apiClient), {
    successMessage: 'Listing updated successfully',
  });
  const deleteListing = useApiMutation(apiClient.deleteListing.bind(apiClient), {
    successMessage: 'Listing deleted successfully',
  });
  const updateListingStatus = useApiMutation(apiClient.updateListingStatus.bind(apiClient), {
    successMessage: 'Listing status updated successfully',
  });

  return {
    getListings,
    getListing,
    createListing,
    updateListing,
    deleteListing,
    updateListingStatus,
  };
}

// Hook for user operations
export function useUserApi() {
  const getProfile = useApi(apiClient.getUserProfile.bind(apiClient));
  const updateProfile = useApiMutation(apiClient.updateUserProfile.bind(apiClient), {
    successMessage: 'Profile updated successfully',
  });

  return {
    getProfile,
    updateProfile,
  };
}

// Hook for file operations
export function useFileApi() {
  const uploadFile = useApiMutation(apiClient.uploadFile.bind(apiClient), {
    successMessage: 'File uploaded successfully',
  });
  const deleteFile = useApiMutation(apiClient.deleteFile.bind(apiClient), {
    successMessage: 'File deleted successfully',
  });

  return {
    uploadFile,
    deleteFile,
  };
}

// Generic hook for custom API calls
export function useCustomApi<T = any>(options: UseApiOptions = {}) {
  return useApi<T>(async (url: string, requestOptions: RequestInit = {}) => {
    return apiClient.makeAuthenticatedRequest<T>(url, requestOptions);
  }, options);
}