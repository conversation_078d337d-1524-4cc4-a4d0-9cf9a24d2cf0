import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Upload, 
  Download, 
  FileSpreadsheet,
  CheckCircle2,
  AlertTriangle,
  ArrowRight,
  ArrowLeft,
  Info,
  File,
  Database
} from 'lucide-react';
import { useImportListingsCsvMutation } from '@/hooks/useQueryApi';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface CsvImportModalProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

type ImportStep = 'prepare' | 'upload' | 'results';

export default function CsvImportModal({ children, onSuccess }: CsvImportModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<ImportStep>('prepare');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [importResults, setImportResults] = useState<any>(null);
  const [dragOver, setDragOver] = useState(false);

  const csvImportMutation = useImportListingsCsvMutation({
    showSuccessToast: false,
    onSuccess: (data) => {
      setImportResults(data);
      setCurrentStep('results');
      if (data.data.created.length > 0) {
        toast({
          title: "Import Completed",
          description: `Successfully imported ${data.data.created.length} listings`,
        });
      }
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      toast({
        title: "Import Failed",
        description: "Failed to import CSV file. Please check the file format and try again.",
        variant: "destructive",
      });
    }
  });

  const steps = [
    { id: 'prepare', title: 'Prepare', icon: Info },
    { id: 'upload', title: 'Upload', icon: Upload },
    { id: 'results', title: 'Results', icon: Database }
  ];

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
        setCsvFile(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select a valid CSV file.",
          variant: "destructive",
        });
      }
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
        setCsvFile(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select a valid CSV file.",
          variant: "destructive",
        });
      }
    }
  };

  const downloadTemplate = () => {
    const a = document.createElement('a');
    a.href = '/business_listings_sample.csv';
    a.download = 'business_listings_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleImport = () => {
    if (csvFile) {
      csvImportMutation.mutate(csvFile);
    }
  };

  const resetModal = () => {
    setCsvFile(null);
    setImportResults(null);
    setCurrentStep('prepare');
    setIsOpen(false);
  };

  const nextStep = () => {
    if (currentStep === 'prepare') {
      setCurrentStep('upload');
    } else if (currentStep === 'upload' && csvFile) {
      handleImport();
    }
  };

  const prevStep = () => {
    if (currentStep === 'upload') {
      setCurrentStep('prepare');
    }
  };

  const getCurrentStepIndex = () => steps.findIndex(step => step.id === currentStep);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-3xl max-h-[85vh] flex flex-col p-0">
        {/* Header with Progress */}
        <div className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <DialogHeader className="space-y-4">
            <DialogTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileSpreadsheet className="h-6 w-6 text-blue-600" />
              </div>
              Bulk Import Listings
            </DialogTitle>
            
            {/* Step Progress */}
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = currentStep === step.id;
                const isCompleted = getCurrentStepIndex() > index;
                
                return (
                  <React.Fragment key={step.id}>
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all",
                        isActive ? "bg-blue-600 text-white" : 
                        isCompleted ? "bg-green-600 text-white" : 
                        "bg-gray-200 text-gray-600"
                      )}>
                        {isCompleted ? (
                          <CheckCircle2 className="h-4 w-4" />
                        ) : (
                          <Icon className="h-4 w-4" />
                        )}
                      </div>
                      <span className={cn(
                        "text-sm font-medium transition-colors",
                        isActive ? "text-blue-600" : 
                        isCompleted ? "text-green-600" : 
                        "text-gray-500"
                      )}>
                        {step.title}
                      </span>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={cn(
                        "flex-1 h-0.5 mx-4 transition-colors",
                        getCurrentStepIndex() > index ? "bg-green-600" : "bg-gray-200"
                      )} />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </DialogHeader>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentStep === 'prepare' && (
            <div className="space-y-6">
              {/* Template Download */}
              <Card className="border-2 border-dashed border-blue-200 bg-blue-50/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <Download className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-blue-900">Download Template</h3>
                        <p className="text-blue-700">Get the correct CSV format for your import</p>
                      </div>
                    </div>
                    <Button onClick={downloadTemplate} size="lg" className="bg-blue-600 hover:bg-blue-700">
                      <Download className="h-4 w-4 mr-2" />
                      Get Template
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Requirements Grid */}
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                      <h3 className="font-semibold">Required Fields</h3>
                    </div>
                    <div className="space-y-2">
                      {['Business Name', 'Industry', 'Asking Price', 'Status'].map((field) => (
                        <div key={field} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full" />
                          <span className="text-sm">{field}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Info className="h-5 w-5 text-blue-600" />
                      <h3 className="font-semibold">Format Requirements</h3>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div>• CSV file with semicolon (;) separators</div>
                      <div>• First row must contain headers</div>
                      <div>• Use UTF-8 encoding</div>
                      <div>• Maximum file size: 10MB</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Optional Fields */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Info className="h-4 w-4 text-blue-600" />
                    Optional Fields
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      'Cash Flow/SDE', 'Annual Revenue', 'General Location',
                      'Year Established', 'Number of Employees', 'Owner Hours per Week',
                      'Brief Description', 'Reason for Sale'
                    ].map((field) => (
                      <Badge key={field} variant="secondary" className="justify-start text-xs">
                        {field}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {currentStep === 'upload' && (
            <div className="space-y-6">
              {/* File Drop Zone */}
              <div
                className={cn(
                  "border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200",
                  dragOver 
                    ? "border-blue-500 bg-blue-50 scale-105" 
                    : csvFile 
                      ? "border-green-500 bg-green-50" 
                      : "border-gray-300 hover:border-gray-400"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {csvFile ? (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="p-4 bg-green-100 rounded-full">
                        <File className="h-8 w-8 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-green-900">{csvFile.name}</h3>
                      <p className="text-green-700">
                        {(csvFile.size / 1024).toFixed(1)} KB • Ready to import
                      </p>
                    </div>
                    <Button 
                      variant="outline" 
                      onClick={() => setCsvFile(null)}
                      className="border-green-200 text-green-700 hover:bg-green-50"
                    >
                      Choose Different File
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="p-4 bg-gray-100 rounded-full">
                        <Upload className="h-8 w-8 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Drop your CSV file here</h3>
                      <p className="text-gray-600 mb-4">or click to browse your files</p>
                      <input
                        type="file"
                        accept=".csv"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="csv-upload"
                        disabled={csvImportMutation.isPending}
                      />
                      <Button 
                        size="lg"
                        onClick={() => document.getElementById('csv-upload')?.click()}
                        disabled={csvImportMutation.isPending}
                      >
                        <FileSpreadsheet className="mr-2 h-4 w-4" />
                        Select CSV File
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Import Preview */}
              {csvFile && (
                <Card className="border-blue-200 bg-blue-50/50">
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-blue-600" />
                      Ready to Import
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">File:</span>
                        <div className="font-medium">{csvFile.name}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Size:</span>
                        <div className="font-medium">{(csvFile.size / 1024).toFixed(1)} KB</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {currentStep === 'results' && importResults && (
            <div className="space-y-6">
              {/* Results Summary */}
              <Card className="border-green-200 bg-green-50/50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-green-900">Import Complete</h3>
                      <p className="text-green-700">Your listings have been processed</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600 mb-1">
                        {importResults.data.created.length}
                      </div>
                      <div className="text-sm text-green-700 font-medium">Successfully Imported</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-red-600 mb-1">
                        {importResults.data.failed.length}
                      </div>
                      <div className="text-sm text-red-700 font-medium">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-gray-900 mb-1">
                        {importResults.data.created.length + importResults.data.failed.length}
                      </div>
                      <div className="text-sm text-gray-700 font-medium">Total Processed</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Error Details */}
              {importResults.data.failed.length > 0 && (
                <Card className="border-red-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <h3 className="font-semibold text-red-900">
                        Failed Imports ({importResults.data.failed.length})
                      </h3>
                    </div>
                    <div className="max-h-48 overflow-y-auto space-y-2">
                      {importResults.data.failed.slice(0, 10).map((failure: any, index: number) => (
                        <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
                          <div className="font-medium text-red-900">Row {failure.index + 1}</div>
                          <div className="text-red-700">{failure.error}</div>
                        </div>
                      ))}
                      {importResults.data.failed.length > 10 && (
                        <div className="text-center py-2 text-sm text-gray-600">
                          And {importResults.data.failed.length - 10} more errors...
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-between">
          <div>
            {currentStep !== 'prepare' && currentStep !== 'results' && (
              <Button variant="outline" onClick={prevStep}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
          </div>
          
          <div className="flex gap-3">
            {currentStep === 'results' ? (
              <Button onClick={resetModal} size="lg" className="min-w-32">
                Done
              </Button>
            ) : currentStep === 'upload' ? (
              <>
                <Button variant="outline" onClick={resetModal}>
                  Cancel
                </Button>
                <Button 
                  onClick={nextStep}
                  disabled={!csvFile || csvImportMutation.isPending}
                  size="lg"
                  className="min-w-32"
                >
                  {csvImportMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      Import Listings
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={resetModal}>
                  Cancel
                </Button>
                <Button onClick={nextStep} size="lg">
                  Continue
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 