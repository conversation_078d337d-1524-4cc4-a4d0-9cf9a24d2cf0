import React, { useState, useCallback } from 'react';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useCreateListingMutation, useUpdateListingMutation, useSaveDraftListingMutation } from '@/hooks/useQueryApi';
import type { CreateListingRequest, UpdateListingRequest, SaveDraftListingRequest } from '@/lib/api-client';
import type { ListingFormData } from '@/types';
import { parseCurrency, formatCurrencyInput } from '@/lib/formatters';
import { listingValidationSchema, getFieldDisplayName } from '@/models/listingValidation';
import { 
  Sparkles, 
  Save, 
  Eye, 
  Plus,
  X
} from 'lucide-react';
import { ButtonSpinner } from '@/components/ui/loading-overlay';

const industries = [
  'Restaurant',
  'Manufacturing', 
  'Retail',
  'Service',
  'Healthcare',
  'Auto',
  'Technology',
  'Other'
];

const statuses = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'confidential', label: 'Confidential' },
  { value: 'under_contract', label: 'Under Contract' },
  { value: 'sold', label: 'Sold' },
  { value: 'expired', label: 'Expired' },
  { value: 'withdrawn', label: 'Withdrawn' }
];

interface ListingFormProps {
  initialData?: Partial<ListingFormData>;
  isEditing?: boolean;
  listingId?: string;
}

export default function ListingForm({ initialData = {}, isEditing = false, listingId }: ListingFormProps) {
  const navigate = useNavigate();
  const createListingMutation = useCreateListingMutation();
  const updateListingMutation = useUpdateListingMutation();
  const saveDraftListingMutation = useSaveDraftListingMutation();

  // Initialize form data with default values
  const [formData, setFormData] = useState<ListingFormData>({
    // Basic Information
    businessName: initialData.businessName || '',
    industry: initialData.industry || '',
    askingPrice: initialData.askingPrice || '',
    cashFlow: initialData.cashFlow || '',
    status: initialData.status || 'active',
    annualRevenue: initialData.annualRevenue || '',
    location: initialData.location || '',
    yearEstablished: initialData.yearEstablished || '',
    employees: initialData.employees || '',
    ownerHours: initialData.ownerHours || '',

    // Business Overview
    businessDescription: initialData.businessDescription || '',
    briefDescription: initialData.briefDescription || '',

    // Financial Details
    revenue2023: initialData.revenue2023 || '',
    ebitda2023: initialData.ebitda2023 || '',
    inventoryValue: initialData.inventoryValue || '',
    realEstateStatus: initialData.realEstateStatus || '',
    assetsIncluded: initialData.assetsIncluded || '',
    leaseDetails: initialData.leaseDetails || '',

    // Operations
    businessModel: initialData.businessModel || '',
    keyFeatures: initialData.keyFeatures || '',
    competitiveAdvantages: initialData.competitiveAdvantages || '',
    customerBase: initialData.customerBase || '',

    // Growth & Sale Information
    growthOpportunities: initialData.growthOpportunities || '',
    reasonForSale: initialData.reasonForSale || '',
    trainingPeriod: initialData.trainingPeriod || '',
    supportType: initialData.supportType || '',
    financingAvailable: initialData.financingAvailable || false,

    // Additional Details
    equipmentHighlights: initialData.equipmentHighlights || '',
    supplierRelationships: initialData.supplierRelationships || '',
    keyEmployeeInfo: initialData.keyEmployeeInfo || '',
    specialNotes: initialData.specialNotes || '',
  });

  // Validation state
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate a single field
  const validateField = useCallback((fieldName: keyof ListingFormData, value: any) => {
    try {
      // Create test data with current form data but updated field value
      const testData = { ...formData, [fieldName]: value };
      listingValidationSchema.parse(testData);
      
      // Clear error if validation passes
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
      
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Find error for this specific field
        const fieldError = error.errors.find(err => err.path[0] === fieldName);
        if (fieldError) {
          setValidationErrors(prev => ({
            ...prev,
            [fieldName]: fieldError.message
          }));
          return false;
        }
        
        // If no error for this field, clear any existing error
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      }
      return true; // Return true if no error for this specific field
    }
  }, [formData]);

  // Validate form data using Zod schema
  const validateForm = useCallback(() => {
    try {
      listingValidationSchema.parse(formData);
      setValidationErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        const requiredFieldErrors: string[] = [];
        const validationFieldErrors: string[] = [];
        
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            const fieldName = err.path[0] as string;
            errors[fieldName] = err.message;
            
            // Categorize errors for better messaging
            if (err.code === 'too_small' && err.type === 'string') {
              requiredFieldErrors.push(getFieldDisplayName(fieldName));
            } else if (err.code === 'custom' || err.code === 'too_big' || err.code === 'invalid_type') {
              validationFieldErrors.push(`${getFieldDisplayName(fieldName)}: ${err.message}`);
            }
          }
        });
        
        setValidationErrors(errors);
        
        // Show comprehensive error message in toast
        const totalErrors = error.errors.length;
        let toastTitle = "";
        let toastDescription = "";
        
        if (totalErrors === 1) {
          // Single error - show it directly
          const firstError = error.errors[0];
          toastTitle = "Please fix this issue:";
          toastDescription = firstError.message;
        } else {
          // Multiple errors - categorize them
          toastTitle = `Please fix ${totalErrors} issue${totalErrors > 1 ? 's' : ''}:`;
          
          if (requiredFieldErrors.length > 0) {
            toastDescription += `Missing required fields: ${requiredFieldErrors.join(', ')}.`;
          }
          
          if (validationFieldErrors.length > 0) {
            if (toastDescription) toastDescription += "\n\n";
            toastDescription += `Validation errors:\n• ${validationFieldErrors.join('\n• ')}`;
          }
          
          // Add helpful tip for multiple errors
          if (totalErrors > 3) {
            toastDescription += `\n\n💡 Tip: Look for red borders around fields that need attention.`;
          }
        }
        
        // Fallback if no description was built
        if (!toastDescription) {
          const firstError = error.errors[0];
          toastTitle = "Validation Error";
          toastDescription = firstError ? firstError.message : "Please check your form data.";
        }
        
        toast({
          title: toastTitle,
          description: toastDescription,
          variant: "destructive",
          duration: Math.min(10000, Math.max(5000, totalErrors * 2000)), // Dynamic duration based on error count
        });
      }
      return false;
    }
  }, [formData]);

  // Calculate form completion progress
  const calculateProgress = useCallback(() => {
    const requiredFields = [
      'businessName',
      'industry', 
      'askingPrice',
      'cashFlow',
      'status'
    ];
    
    const allFields = Object.keys(formData);
    const filledFields = Object.values(formData).filter(value => 
      value !== '' && value !== false && value !== null && value !== undefined
    ).length;
    
    const requiredFilledFields = requiredFields.filter(field => {
      const value = formData[field as keyof ListingFormData];
      return value !== '' && value !== false && value !== null && value !== undefined;
    }).length;

    const requiredProgress = (requiredFilledFields / requiredFields.length) * 100;
    const totalProgress = (filledFields / allFields.length) * 100;

    return {
      required: requiredProgress,
      total: totalProgress,
    };
  }, [formData]);

  // Generic input change handler
  const handleInputChange = useCallback((field: keyof ListingFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // Field blur handler for real-time validation
  const handleFieldBlur = useCallback((field: keyof ListingFormData, value: string | boolean) => {
    validateField(field, value);
  }, [validateField]);

  // Currency input change handler
  const handleCurrencyChange = useCallback((field: keyof ListingFormData, value: string) => {
    const formatted = formatCurrencyInput(value);
    setFormData(prev => ({
      ...prev,
      [field]: formatted,
    }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // AI generation handler (mock implementation)
  const handleAiGenerate = useCallback(async (field: keyof ListingFormData) => {
    toast({
      title: "AI Generation",
      description: "AI content generation is coming soon!",
    });

    // Mock AI generation - in real implementation, this would call an AI service
    setTimeout(() => {
      let generatedContent = '';
      
      switch (field) {
        case 'businessDescription':
          generatedContent = `This ${formData.industry} business has been serving the ${formData.location} area with dedication and excellence. With ${formData.employees} employees and established operations, it represents a solid investment opportunity with proven cash flow.`;
          break;
        case 'businessModel':
          generatedContent = `The business operates on a proven model within the ${formData.industry} industry, leveraging established processes and customer relationships to generate consistent revenue.`;
          break;
        case 'growthOpportunities':
          generatedContent = `Significant expansion opportunities include market penetration, service diversification, and operational optimization. The established customer base provides a foundation for sustainable growth.`;
          break;
        case 'reasonForSale':
          generatedContent = `The owner is looking to retire after years of successful operation and seeks a qualified buyer to continue the business legacy.`;
          break;
        default:
          generatedContent = `Generated content for ${field} field.`;
      }

      setFormData(prev => ({
        ...prev,
        [field]: generatedContent,
      }));

      toast({
        title: "Content Generated",
        description: `AI has generated content for ${field}. You can edit it as needed.`,
      });
    }, 1500);
  }, [formData.industry, formData.location, formData.employees]);

  // Convert form data to API format
  const convertFormDataToApiFormat = useCallback((): CreateListingRequest | UpdateListingRequest => {
    // Helper function to safely parse currency values
    const safeParseCurrency = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      return parseCurrency(value);
    };

    // Helper function to safely parse integer values
    const safeParseInt = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      const num = parseInt(value);
      return isNaN(num) ? undefined : num;
    };

    // Build the base request object
    const baseRequest: CreateListingRequest | UpdateListingRequest = {
      businessName: formData.businessName.trim(),
      industry: formData.industry.trim(),
    };

    // Add optional fields only if they have values
    const askingPrice = safeParseCurrency(formData.askingPrice);
    if (askingPrice !== undefined) {
      baseRequest.askingPrice = askingPrice;
    }

    const cashFlow = safeParseCurrency(formData.cashFlow);
    if (cashFlow !== undefined) {
      baseRequest.cashFlowSde = cashFlow;
    }

    const annualRevenue = safeParseCurrency(formData.annualRevenue);
    if (annualRevenue !== undefined) {
      baseRequest.annualRevenue = annualRevenue;
    }

    if (formData.status) {
      baseRequest.status = formData.status;
    }

    if (formData.location && formData.location.trim()) {
      baseRequest.generalLocation = formData.location.trim();
    }

    const yearEstablished = safeParseInt(formData.yearEstablished);
    if (yearEstablished !== undefined) {
      baseRequest.yearEstablished = yearEstablished;
    }

    const employees = safeParseInt(formData.employees);
    if (employees !== undefined) {
      baseRequest.employees = employees;
    }

    const ownerHours = safeParseInt(formData.ownerHours);
    if (ownerHours !== undefined) {
      baseRequest.ownerHoursWeek = ownerHours;
    }

    // Build details object only if we have any detail fields
    const details: any = {};
    let hasDetails = false;

    if (formData.businessDescription && formData.businessDescription.trim()) {
      details.businessDescription = formData.businessDescription.trim();
      hasDetails = true;
    }

    if (formData.briefDescription && formData.briefDescription.trim()) {
      details.briefDescription = formData.briefDescription.trim();
      hasDetails = true;
    }

    // Financial details
    const financialDetails: any = {};
    let hasFinancialDetails = false;

    const revenue2023 = safeParseCurrency(formData.revenue2023);
    if (revenue2023 !== undefined) {
      financialDetails.revenue2023 = revenue2023;
      hasFinancialDetails = true;
    }

    const ebitda2023 = safeParseCurrency(formData.ebitda2023);
    if (ebitda2023 !== undefined) {
      financialDetails.ebitda = ebitda2023; // Note: API expects 'ebitda', not 'ebitda_2023'
      hasFinancialDetails = true;
    }

    const inventoryValue = safeParseCurrency(formData.inventoryValue);
    if (inventoryValue !== undefined) {
      financialDetails.inventoryValue = inventoryValue;
      hasFinancialDetails = true;
    }

    if (formData.assetsIncluded && formData.assetsIncluded.trim()) {
      financialDetails.assetsIncluded = formData.assetsIncluded.split('\n').filter(line => line.trim() !== '');
      hasFinancialDetails = true;
    }

    if (hasFinancialDetails) {
      details.financialDetails = financialDetails;
      hasDetails = true;
    }

    // Operations details
    const operations: any = {};
    let hasOperations = false;

    if (formData.businessModel && formData.businessModel.trim()) {
      operations.businessModel = formData.businessModel.trim();
      hasOperations = true;
    }

    if (formData.keyFeatures && formData.keyFeatures.trim()) {
      operations.keyFeatures = formData.keyFeatures.split('\n').filter(line => line.trim() !== '');
      hasOperations = true;
    }

    if (formData.competitiveAdvantages && formData.competitiveAdvantages.trim()) {
      operations.competitiveAdvantages = formData.competitiveAdvantages.split('\n').filter(line => line.trim() !== '');
      hasOperations = true;
    }

    if (formData.customerBase && formData.customerBase.trim()) {
      operations.customerBase = formData.customerBase.trim();
      hasOperations = true;
    }

    if (hasOperations) {
      details.operations = operations;
      hasDetails = true;
    }

    // Other details
    if (formData.growthOpportunities && formData.growthOpportunities.trim()) {
      details.growthOpportunities = formData.growthOpportunities.split('\n').filter(line => line.trim() !== '');
      hasDetails = true;
    }

    if (formData.reasonForSale && formData.reasonForSale.trim()) {
      details.reasonForSale = formData.reasonForSale.trim();
      hasDetails = true;
    }

    if (formData.trainingPeriod && formData.trainingPeriod.trim()) {
      details.trainingPeriod = formData.trainingPeriod.trim();
      hasDetails = true;
    }

    if (formData.supportType && formData.supportType.trim()) {
      details.supportType = formData.supportType.trim();
      hasDetails = true;
    }

    // Always include financing_available as it's a boolean
    details.financingAvailable = formData.financingAvailable;
    hasDetails = true;

    if (formData.equipmentHighlights && formData.equipmentHighlights.trim()) {
      details.equipmentHighlights = formData.equipmentHighlights.split('\n').filter(line => line.trim() !== '');
      hasDetails = true;
    }

    if (formData.supplierRelationships && formData.supplierRelationships.trim()) {
      details.supplierRelationships = formData.supplierRelationships.trim();
      hasDetails = true;
    }

    if (formData.realEstateStatus && formData.realEstateStatus.trim()) {
      details.realEstateStatus = formData.realEstateStatus.trim();
      hasDetails = true;
    }

    if (formData.leaseDetails && formData.leaseDetails.trim()) {
      details.leaseDetails = {
        leaseTerms: formData.leaseDetails.trim(), // API expects 'lease_terms', not 'details'
      };
      hasDetails = true;
    }

    // Only add details if we have any
    if (hasDetails) {
      baseRequest.details = details;
    }

    return baseRequest;
  }, [formData]);

  // Save as draft
  const handleSaveDraft = useCallback(async () => {
    setIsSubmitting(true);
    try {
      const apiData = convertFormDataToApiFormat();
      // Ensure status is set to draft
      apiData.status = 'draft';

      console.log('Saving draft with data:', JSON.stringify(apiData, null, 2));

      if (isEditing && listingId) {
        // For editing existing listings (including drafts), use the regular update endpoint
        await updateListingMutation.mutateAsync({
          listingId,
          listingData: apiData as UpdateListingRequest
        });
      } else {
        // For new drafts, use the dedicated draft endpoint which handles incomplete data
        await saveDraftListingMutation.mutateAsync(apiData as SaveDraftListingRequest);
      }

      toast({
        title: "Draft Saved",
        description: "Your listing has been saved as a draft.",
      });
    } catch (error) {
      console.error('Save draft error:', error);
      // Log more detailed error information
      if (error && typeof error === 'object' && 'message' in error) {
        console.error('Error details:', error);
      }
      toast({
        title: "Error Saving Draft",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [convertFormDataToApiFormat, isEditing, listingId, updateListingMutation, saveDraftListingMutation]);

  // Save and view
  const handleSaveAndView = useCallback(async () => {
    // Validate form before saving
    if (!validateForm()) {
      return;
    }
    setIsSubmitting(true);

    try {
      const apiData = convertFormDataToApiFormat();

      console.log('Saving listing with data:', JSON.stringify(apiData, null, 2));

      let result;
      if (isEditing && listingId) {
        result = await updateListingMutation.mutateAsync({ 
          listingId, 
          listingData: apiData as UpdateListingRequest 
        });
        navigate(`/listings/${listingId}`);
      } else {
        result = await createListingMutation.mutateAsync(apiData as CreateListingRequest);
        if (result?.data?.id) {
          navigate(`/listings/${result.data.id}`);
        } else {
          navigate('/listings');
        }
      }

      toast({
        title: isEditing ? "Listing Updated" : "Listing Created",
        description: isEditing ? 
          "Your listing has been updated successfully." : 
          "Your listing has been created successfully.",
      });
    } catch (error) {
      console.error('Save and view error:', error);
      // Log more detailed error information
      if (error && typeof error === 'object' && 'message' in error) {
        console.error('Error details:', error);
      }
      toast({
        title: "Error Saving Listing",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, convertFormDataToApiFormat, isEditing, listingId, updateListingMutation, createListingMutation, navigate]);

  // Save and add another
  const handleSaveAndAddAnother = useCallback(async () => {
    // Validate form before saving
    if (!validateForm()) {
      return;
    }
    setIsSubmitting(true);

    try {
      const apiData = convertFormDataToApiFormat();

      console.log('Saving listing (add another) with data:', JSON.stringify(apiData, null, 2));

      await createListingMutation.mutateAsync(apiData as CreateListingRequest);

      toast({
        title: "Listing Created",
        description: "Your listing has been created. You can add another one now.",
      });

      // Reset form for new listing
      setFormData({
        businessName: '',
        industry: '',
        askingPrice: '',
        cashFlow: '',
        status: 'active',
        annualRevenue: '',
        location: '',
        yearEstablished: '',
        employees: '',
        ownerHours: '',
        businessDescription: '',
        briefDescription: '',
        revenue2023: '',
        ebitda2023: '',
        inventoryValue: '',
        realEstateStatus: '',
        assetsIncluded: '',
        leaseDetails: '',
        businessModel: '',
        keyFeatures: '',
        competitiveAdvantages: '',
        customerBase: '',
        growthOpportunities: '',
        reasonForSale: '',
        trainingPeriod: '',
        supportType: '',
        financingAvailable: false,
        equipmentHighlights: '',
        supplierRelationships: '',
        keyEmployeeInfo: '',
        specialNotes: '',
      });
      
      // Clear validation errors on reset
      setValidationErrors({});
    } catch (error) {
      console.error('Save and add another error:', error);
      // Log more detailed error information
      if (error && typeof error === 'object' && 'message' in error) {
        console.error('Error details:', error);
      }
      toast({
        title: "Error Saving Listing",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, convertFormDataToApiFormat, createListingMutation]);

  const progress = calculateProgress();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Listing Form
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                <Label htmlFor="businessName">Business Name <span className="text-red-500">*</span></Label>
              <Input
                id="businessName"
                name="business-name"
                autoComplete="organization"
                value={formData.businessName}
                onChange={(e) => handleInputChange('businessName', e.target.value)}
                onBlur={(e) => handleFieldBlur('businessName', e.target.value)}
                placeholder="Enter business name"
                data-form-type="business"
                data-field="company-name"
                className={`transition-all duration-200 ${validationErrors.businessName ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.businessName && <p className="text-xs text-red-500">{validationErrors.businessName}</p>}
            </div>
              
              <div className="space-y-2">
                <Label htmlFor="industry">Industry <span className="text-red-500">*</span></Label>
                <Select 
                  value={formData.industry} 
                  onValueChange={(value) => handleInputChange('industry', value)}
                  name="industry"
                >
                  <SelectTrigger 
                    data-form-type="business"
                    data-field="industry"
                    data-category="classification"
                    disabled={isSubmitting}
                  >
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map(industry => (
                      <SelectItem key={industry} value={industry.toLowerCase()}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.industry && <p className="text-xs text-red-500">{validationErrors.industry}</p>}
              </div>

            <div className="space-y-2">
              <Label htmlFor="askingPrice">Asking Price <span className="text-red-500">*</span></Label>
              <Input
                id="askingPrice"
                name="asking-price"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.askingPrice}
                onChange={(e) => handleCurrencyChange('askingPrice', e.target.value)}
                onBlur={(e) => handleFieldBlur('askingPrice', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="price"
                data-currency="USD"
                className={`transition-all duration-200 ${validationErrors.askingPrice ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.askingPrice && <p className="text-xs text-red-500">{validationErrors.askingPrice}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="cashFlow">Cash Flow/SDE <span className="text-red-500">*</span></Label>
              <Input
                id="cashFlow"
                name="cash-flow"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.cashFlow}
                onChange={(e) => handleCurrencyChange('cashFlow', e.target.value)}
                onBlur={(e) => handleFieldBlur('cashFlow', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="cash-flow"
                data-currency="USD"
                className={`transition-all duration-200 ${validationErrors.cashFlow ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.cashFlow && <p className="text-xs text-red-500">{validationErrors.cashFlow}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status <span className="text-red-500">*</span></Label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => handleInputChange('status', value)}
                name="listing-status"
              >
                <SelectTrigger
                  data-form-type="business"
                  data-field="status"
                  data-category="sale-status"
                  disabled={isSubmitting}
                >
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.status && <p className="text-xs text-red-500">{validationErrors.status}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="annualRevenue">Annual Revenue</Label>
              <Input
                id="annualRevenue"
                name="annual-revenue"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.annualRevenue}
                onChange={(e) => handleCurrencyChange('annualRevenue', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="revenue"
                data-currency="USD"
                className={`transition-all duration-200 ${validationErrors.annualRevenue ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.annualRevenue && <p className="text-xs text-red-500">{validationErrors.annualRevenue}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">General Location</Label>
              <Input
                id="location"
                name="location"
                type="text"
                autoComplete="address-level2"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="North Tampa, FL"
                data-form-type="location"
                data-field="city-state"
                className={`transition-all duration-200 ${validationErrors.location ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.location && <p className="text-xs text-red-500">{validationErrors.location}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="yearEstablished">Year Established</Label>
              <Input
                id="yearEstablished"
                name="year-established"
                type="number"
                inputMode="numeric"
                autoComplete="off"
                min="1800"
                max="2025"
                value={formData.yearEstablished}
                onChange={(e) => handleInputChange('yearEstablished', e.target.value)}
                placeholder="2020"
                data-form-type="date"
                data-field="year"
                className={`transition-all duration-200 ${validationErrors.yearEstablished ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.yearEstablished && <p className="text-xs text-red-500">{validationErrors.yearEstablished}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="employees">Number of Employees</Label>
              <Input
                id="employees"
                name="employee-count"
                type="number"
                inputMode="numeric"
                autoComplete="off"
                min="0"
                value={formData.employees}
                onChange={(e) => handleInputChange('employees', e.target.value)}
                placeholder="5"
                data-form-type="business"
                data-field="employee-count"
                className={`transition-all duration-200 ${validationErrors.employees ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.employees && <p className="text-xs text-red-500">{validationErrors.employees}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ownerHours">Owner Hours/Week</Label>
              <Input
                id="ownerHours"
                name="owner-hours-per-week"
                type="number"
                inputMode="numeric"
                autoComplete="off"
                min="0"
                max="168"
                value={formData.ownerHours}
                onChange={(e) => handleInputChange('ownerHours', e.target.value)}
                placeholder="40"
                data-form-type="business"
                data-field="hours"
                className={`transition-all duration-200 ${validationErrors.ownerHours ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.ownerHours && <p className="text-xs text-red-500">{validationErrors.ownerHours}</p>}
            </div>
          </div>

          <div className="flex items-center space-x-2 pt-4">
            <Checkbox
              id="confidential"
              checked={formData.status === 'confidential'}
              onCheckedChange={(checked) => handleInputChange('status', checked ? 'confidential' : 'active')}
              disabled={isSubmitting}
            />
            <Label htmlFor="confidential">Mark as Confidential</Label>
          </div>
        </CardContent>
      </Card>

      {/* Business Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Business Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="businessDescription">Business Description</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAiGenerate('businessDescription')}
                className="flex items-center gap-2"
                disabled={isSubmitting}
              >
                <Sparkles className="h-4 w-4" />
                Generate with AI
              </Button>
            </div>
            <Textarea
              id="businessDescription"
              name="business-description"
              autoComplete="off"
              value={formData.businessDescription}
              onChange={(e) => handleInputChange('businessDescription', e.target.value)}
              placeholder="Detailed business description (up to 5,000 characters)"
              rows={6}
              data-form-type="text"
              data-field="description"
              data-length="long"
              className={`transition-all duration-200 ${validationErrors.businessDescription ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.businessDescription && <p className="text-xs text-red-500">{validationErrors.businessDescription}</p>}
            <p className="text-xs text-muted-foreground">
              {formData.businessDescription.length}/5,000 characters
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="briefDescription">Brief Description for Portfolio</Label>
            <Textarea
              id="briefDescription"
              name="brief-description"
              autoComplete="off"
              value={formData.briefDescription}
              onChange={(e) => handleInputChange('briefDescription', e.target.value)}
              placeholder="Brief description for portfolio listings (up to 500 characters)"
              rows={3}
              data-form-type="text"
              data-field="summary"
              data-length="short"
              className={`transition-all duration-200 ${validationErrors.briefDescription ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.briefDescription && <p className="text-xs text-red-500">{validationErrors.briefDescription}</p>}
            <p className="text-xs text-muted-foreground">
              {formData.briefDescription.length}/500 characters
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Financial Details */}
      <Card>
        <CardHeader>
          <CardTitle>Financial Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="revenue2023">2023 Revenue</Label>
              <Input
                id="revenue2023"
                name="revenue-2023"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.revenue2023}
                onChange={(e) => handleCurrencyChange('revenue2023', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="revenue-annual"
                data-currency="USD"
                data-year="2023"
                className={`transition-all duration-200 ${validationErrors.revenue2023 ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.revenue2023 && <p className="text-xs text-red-500">{validationErrors.revenue2023}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ebitda2023">2023 EBITDA</Label>
              <Input
                id="ebitda2023"
                name="ebitda-2023"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.ebitda2023}
                onChange={(e) => handleCurrencyChange('ebitda2023', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="ebitda"
                data-currency="USD"
                data-year="2023"
                className={`transition-all duration-200 ${validationErrors.ebitda2023 ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.ebitda2023 && <p className="text-xs text-red-500">{validationErrors.ebitda2023}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="inventoryValue">Inventory Value</Label>
              <Input
                id="inventoryValue"
                name="inventory-value"
                type="text"
                inputMode="numeric"
                autoComplete="off"
                value={formData.inventoryValue}
                onChange={(e) => handleCurrencyChange('inventoryValue', e.target.value)}
                placeholder="$0"
                data-form-type="currency"
                data-field="inventory"
                data-currency="USD"
                className={`transition-all duration-200 ${validationErrors.inventoryValue ? 'border-red-500' : ''}`}
                disabled={isSubmitting}
              />
              {validationErrors.inventoryValue && <p className="text-xs text-red-500">{validationErrors.inventoryValue}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="realEstateStatus">Real Estate Status</Label>
              <Select 
                value={formData.realEstateStatus} 
                onValueChange={(value) => handleInputChange('realEstateStatus', value)}
                name="real-estate-status"
              >
                <SelectTrigger
                  data-form-type="real-estate"
                  data-field="ownership-status"
                  data-category="property"
                  disabled={isSubmitting}
                >
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="leased">Leased</SelectItem>
                  <SelectItem value="owned">Owned</SelectItem>
                  <SelectItem value="available-separately">Available Separately</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors.realEstateStatus && <p className="text-xs text-red-500">{validationErrors.realEstateStatus}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assetsIncluded">Assets Included</Label>
            <Textarea
              id="assetsIncluded"
              name="assets-included"
              autoComplete="off"
              value={formData.assetsIncluded}
              onChange={(e) => handleInputChange('assetsIncluded', e.target.value)}
              placeholder="List all assets included in the sale"
              rows={3}
              data-form-type="business"
              data-field="assets"
              data-category="inventory"
              className={`transition-all duration-200 ${validationErrors.assetsIncluded ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.assetsIncluded && <p className="text-xs text-red-500">{validationErrors.assetsIncluded}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="leaseDetails">Lease Details</Label>
            <Textarea
              id="leaseDetails"
              value={formData.leaseDetails}
              onChange={(e) => handleInputChange('leaseDetails', e.target.value)}
              placeholder="Lease terms, rent amount, expiration date, etc."
              rows={3}
              className={`transition-all duration-200 ${validationErrors.leaseDetails ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.leaseDetails && <p className="text-xs text-red-500">{validationErrors.leaseDetails}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Operations Section */}
      <Card>
        <CardHeader>
          <CardTitle>Operations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="businessModel">Business Model Description</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAiGenerate('businessModel')}
                className="flex items-center gap-2"
                disabled={isSubmitting}
              >
                <Sparkles className="h-4 w-4" />
                Generate with AI
              </Button>
            </div>
            <Textarea
              id="businessModel"
              value={formData.businessModel}
              onChange={(e) => handleInputChange('businessModel', e.target.value)}
              placeholder="Describe how the business operates"
              rows={4}
              className={`transition-all duration-200 ${validationErrors.businessModel ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.businessModel && <p className="text-xs text-red-500">{validationErrors.businessModel}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="keyFeatures">Key Features</Label>
            <Textarea
              id="keyFeatures"
              value={formData.keyFeatures}
              onChange={(e) => handleInputChange('keyFeatures', e.target.value)}
              placeholder="• Feature 1&#10;• Feature 2&#10;• Feature 3"
              rows={4}
              className={`transition-all duration-200 ${validationErrors.keyFeatures ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.keyFeatures && <p className="text-xs text-red-500">{validationErrors.keyFeatures}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="competitiveAdvantages">Competitive Advantages</Label>
            <Textarea
              id="competitiveAdvantages"
              value={formData.competitiveAdvantages}
              onChange={(e) => handleInputChange('competitiveAdvantages', e.target.value)}
              placeholder="What sets this business apart from competitors"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.competitiveAdvantages ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.competitiveAdvantages && <p className="text-xs text-red-500">{validationErrors.competitiveAdvantages}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerBase">Customer Base</Label>
            <Textarea
              id="customerBase"
              value={formData.customerBase}
              onChange={(e) => handleInputChange('customerBase', e.target.value)}
              placeholder="Describe the customer demographics and relationships"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.customerBase ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.customerBase && <p className="text-xs text-red-500">{validationErrors.customerBase}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Growth & Sale Information */}
      <Card>
        <CardHeader>
          <CardTitle>Growth & Sale Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="growthOpportunities">Growth Opportunities</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAiGenerate('growthOpportunities')}
                className="flex items-center gap-2"
                disabled={isSubmitting}
              >
                <Sparkles className="h-4 w-4" />
                Generate with AI
              </Button>
            </div>
            <Textarea
              id="growthOpportunities"
              value={formData.growthOpportunities}
              onChange={(e) => handleInputChange('growthOpportunities', e.target.value)}
              placeholder="Expansion possibilities, market potential, untapped revenue streams"
              rows={4}
              className={`transition-all duration-200 ${validationErrors.growthOpportunities ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.growthOpportunities && <p className="text-xs text-red-500">{validationErrors.growthOpportunities}</p>}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="reasonForSale">Reason for Sale</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAiGenerate('reasonForSale')}
                className="flex items-center gap-2"
                disabled={isSubmitting}
              >
                <Sparkles className="h-4 w-4" />
                Generate with AI
              </Button>
            </div>
            <Textarea
              id="reasonForSale"
              value={formData.reasonForSale}
              onChange={(e) => handleInputChange('reasonForSale', e.target.value)}
              placeholder="Seller motivation and transition timeline"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.reasonForSale ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.reasonForSale && <p className="text-xs text-red-500">{validationErrors.reasonForSale}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="trainingPeriod">Training Period</Label>
                          <Input
              id="trainingPeriod"
              name="training-period"
              type="text"
              autoComplete="off"
              value={formData.trainingPeriod}
              onChange={(e) => handleInputChange('trainingPeriod', e.target.value)}
              placeholder="3 months"
              data-form-type="business"
              data-field="training-duration"
              data-category="support"
              className={`transition-all duration-200 ${validationErrors.trainingPeriod ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
              {validationErrors.trainingPeriod && <p className="text-xs text-red-500">{validationErrors.trainingPeriod}</p>}
            </div>

                          <div className="space-y-2">
                <Label htmlFor="supportType">Support Type</Label>
                <Select 
                  value={formData.supportType} 
                  onValueChange={(value) => handleInputChange('supportType', value)}
                  name="support-type"
                >
                  <SelectTrigger
                    data-form-type="business"
                    data-field="support-method"
                    data-category="training"
                    disabled={isSubmitting}
                  >
                    <SelectValue placeholder="Select support type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="on-site">On-site</SelectItem>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="both">Both</SelectItem>
                  </SelectContent>
                </Select>
                {validationErrors.supportType && <p className="text-xs text-red-500">{validationErrors.supportType}</p>}
              </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="financingAvailable"
              name="financing-available"
              checked={formData.financingAvailable}
              onCheckedChange={(checked) => handleInputChange('financingAvailable', !!checked)}
              data-form-type="business"
              data-field="financing"
              data-category="payment-options"
              disabled={isSubmitting}
            />
            <Label htmlFor="financingAvailable">Financing Available</Label>
          </div>
        </CardContent>
      </Card>

      {/* Additional Details */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="equipmentHighlights">Equipment Highlights</Label>
            <Textarea
              id="equipmentHighlights"
              value={formData.equipmentHighlights}
              onChange={(e) => handleInputChange('equipmentHighlights', e.target.value)}
              placeholder="Key equipment and machinery included"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.equipmentHighlights ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.equipmentHighlights && <p className="text-xs text-red-500">{validationErrors.equipmentHighlights}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="supplierRelationships">Supplier Relationships</Label>
            <Textarea
              id="supplierRelationships"
              value={formData.supplierRelationships}
              onChange={(e) => handleInputChange('supplierRelationships', e.target.value)}
              placeholder="Key supplier information and relationships"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.supplierRelationships ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.supplierRelationships && <p className="text-xs text-red-500">{validationErrors.supplierRelationships}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="keyEmployeeInfo">Key Employee Information</Label>
            <Textarea
              id="keyEmployeeInfo"
              value={formData.keyEmployeeInfo}
              onChange={(e) => handleInputChange('keyEmployeeInfo', e.target.value)}
              placeholder="Information about key employees and their roles"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.keyEmployeeInfo ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.keyEmployeeInfo && <p className="text-xs text-red-500">{validationErrors.keyEmployeeInfo}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="specialNotes">Special Notes</Label>
            <Textarea
              id="specialNotes"
              value={formData.specialNotes}
              onChange={(e) => handleInputChange('specialNotes', e.target.value)}
              placeholder="Any additional important information"
              rows={3}
              className={`transition-all duration-200 ${validationErrors.specialNotes ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            {validationErrors.specialNotes && <p className="text-xs text-red-500">{validationErrors.specialNotes}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <div className="flex gap-4">
              <Button variant="outline" onClick={handleSaveDraft} className="flex items-center gap-2" disabled={isSubmitting}>
                <Save className="h-4 w-4" />
                Save as Draft
              </Button>
              {!isEditing && (
                <Button variant="outline" onClick={handleSaveAndAddAnother} className="flex items-center gap-2" disabled={isSubmitting}>
                  <Plus className="h-4 w-4" />
                  Save & Add Another
                </Button>
              )}
              {isEditing && listingId && (
                <Button 
                  variant="outline" 
                  onClick={() => navigate(`/listings/${listingId}`)}
                  className="flex items-center gap-2"
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
              )}
            </div>
            <Button onClick={handleSaveAndView} className="flex items-center gap-2" disabled={isSubmitting} aria-disabled={isSubmitting}>
              {isSubmitting ? <ButtonSpinner className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {isEditing ? 'Save Changes' : 'Save & View Listing'}
            </Button>
          </div>
        </CardContent>
              </Card>
      </CardContent>
    </Card>
  );
}