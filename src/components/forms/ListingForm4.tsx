import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Building2,
  DollarSign,
  FileText,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Save,
  Eye,
  Sparkles,
  MapPin,
  Calendar,
  Users,
  Clock,
  PieChart,
  Target,
  Briefcase,
  Award,
  Lightbulb,
  HandHeart,
  ChevronDown,
  ChevronUp,
  Star,
  Zap,
  BarChart3,
  Home
} from 'lucide-react';
import { cn } from '@/lib/utils';

const industries = [
  'Restaurant',
  'Manufacturing', 
  'Retail',
  'Service',
  'Healthcare',
  'Auto',
  'Technology',
  'Other'
];

const statuses = [
  { value: 'draft', label: 'Draft', color: 'bg-gray-100 text-gray-800' },
  { value: 'active', label: 'Active', color: 'bg-green-100 text-green-800' },
  { value: 'confidential', label: 'Confidential', color: 'bg-amber-100 text-amber-800' },
  { value: 'under_contract', label: 'Under Contract', color: 'bg-blue-100 text-blue-800' },
  { value: 'sold', label: 'Sold', color: 'bg-purple-100 text-purple-800' },
  { value: 'expired', label: 'Expired', color: 'bg-red-100 text-red-800' },
  { value: 'withdrawn', label: 'Withdrawn', color: 'bg-gray-100 text-gray-800' }
];

interface FormData {
  // Basic Information
  businessName: string;
  industry: string;
  askingPrice: string;
  cashFlow: string;
  status: string;
  annualRevenue: string;
  location: string;
  yearEstablished: string;
  employees: string;
  ownerHours: string;

  // Business Overview
  businessDescription: string;
  briefDescription: string;

  // Financial Details
  revenue2023: string;
  ebitda2023: string;
  inventoryValue: string;
  realEstateStatus: string;
  assetsIncluded: string;
  leaseDetails: string;

  // Operations
  businessModel: string;
  keyFeatures: string;
  competitiveAdvantages: string;
  customerBase: string;

  // Growth & Sale Information
  growthOpportunities: string;
  reasonForSale: string;
  trainingPeriod: string;
  supportType: string;
  financingAvailable: boolean;

  // Additional Details
  equipmentHighlights: string;
  supplierRelationships: string;
  keyEmployeeInfo: string;
  specialNotes: string;
}

interface Section {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ElementType;
  color: string;
  gradientFrom: string;
  gradientTo: string;
  fields: (keyof FormData)[];
}

const sections: Section[] = [
  {
    id: 'essentials',
    title: 'Business Essentials',
    subtitle: 'Core information about your business',
    icon: Building2,
    color: 'blue',
    gradientFrom: 'from-blue-500',
    gradientTo: 'to-cyan-500',
    fields: ['businessName', 'industry', 'location', 'yearEstablished', 'status']
  },
  {
    id: 'financials',
    title: 'Financial Overview',
    subtitle: 'Revenue, pricing, and financial details',
    icon: BarChart3,
    color: 'emerald',
    gradientFrom: 'from-emerald-500',
    gradientTo: 'to-teal-500',
    fields: ['askingPrice', 'cashFlow', 'annualRevenue', 'revenue2023', 'ebitda2023', 'inventoryValue']
  },
  {
    id: 'operations',
    title: 'Operations & Team',
    subtitle: 'How your business runs day-to-day',
    icon: Users,
    color: 'purple',
    gradientFrom: 'from-purple-500',
    gradientTo: 'to-pink-500',
    fields: ['employees', 'ownerHours', 'businessModel', 'realEstateStatus']
  },
  {
    id: 'story',
    title: 'Business Story',
    subtitle: 'Tell buyers what makes your business special',
    icon: Star,
    color: 'amber',
    gradientFrom: 'from-amber-500',
    gradientTo: 'to-orange-500',
    fields: ['businessDescription', 'briefDescription', 'keyFeatures', 'competitiveAdvantages']
  },
  {
    id: 'growth',
    title: 'Growth & Opportunity',
    subtitle: 'Future potential and expansion possibilities',
    icon: TrendingUp,
    color: 'rose',
    gradientFrom: 'from-rose-500',
    gradientTo: 'to-pink-500',
    fields: ['growthOpportunities', 'reasonForSale', 'customerBase', 'supplierRelationships']
  }
];

export default function ModernListingForm() {
  const [formData, setFormData] = useState<FormData>({
    businessName: '',
    industry: '',
    askingPrice: '',
    cashFlow: '',
    status: 'active',
    annualRevenue: '',
    location: '',
    yearEstablished: '',
    employees: '',
    ownerHours: '',
    businessDescription: '',
    briefDescription: '',
    revenue2023: '',
    ebitda2023: '',
    inventoryValue: '',
    realEstateStatus: '',
    assetsIncluded: '',
    leaseDetails: '',
    businessModel: '',
    keyFeatures: '',
    competitiveAdvantages: '',
    customerBase: '',
    growthOpportunities: '',
    reasonForSale: '',
    trainingPeriod: '',
    supportType: '',
    financingAvailable: false,
    equipmentHighlights: '',
    supplierRelationships: '',
    keyEmployeeInfo: '',
    specialNotes: ''
  });

  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['essentials']));
  const [completedSections, setCompletedSections] = useState<Set<string>>(new Set());
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Auto-expand next section when current is completed
  useEffect(() => {
    const checkSectionCompletion = () => {
      const newCompletedSections = new Set<string>();
      
      sections.forEach(section => {
        const requiredFields = section.fields.filter(field => 
          ['businessName', 'industry', 'askingPrice', 'cashFlow'].includes(field)
        );
        
        const hasRequiredFields = requiredFields.every(field => {
          const value = formData[field];
          return value && value.toString().trim() !== '';
        });

        const hasOptionalContent = section.fields.some(field => {
          const value = formData[field];
          return value && value.toString().trim() !== '';
        });

        if (hasRequiredFields || hasOptionalContent) {
          newCompletedSections.add(section.id);
        }
      });

      setCompletedSections(newCompletedSections);
    };

    checkSectionCompletion();
  }, [formData]);

  const handleInputChange = useCallback((field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  const formatCurrency = (value: string) => {
    const numbers = value.replace(/[^\d]/g, '');
    if (!numbers) return '';
    const num = parseInt(numbers);
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(num);
  };

  const handleCurrencyChange = useCallback((field: keyof FormData, value: string) => {
    const formatted = formatCurrency(value);
    setFormData(prev => ({ ...prev, [field]: formatted }));
  }, []);

  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(sectionId)) {
        newExpanded.delete(sectionId);
      } else {
        newExpanded.add(sectionId);
      }
      return newExpanded;
    });
  }, []);

  const calculateProgress = useCallback(() => {
    const totalFields = sections.reduce((acc, section) => acc + section.fields.length, 0);
    const filledFields = Object.values(formData).filter(value => 
      value !== '' && value !== false
    ).length;
    return Math.round((filledFields / totalFields) * 100);
  }, [formData]);

  const handleAiGenerate = useCallback(async (field: keyof FormData) => {
    // Simulate AI generation
    const suggestions = {
      businessDescription: "A well-established business with strong market presence and consistent revenue growth. Known for exceptional customer service and quality products/services.",
      briefDescription: "Profitable business with growth potential in a stable market.",
      businessModel: "B2C retail model with multiple revenue streams including direct sales and service contracts.",
      keyFeatures: "• Established customer base\n• Prime location\n• Modern equipment\n• Trained staff",
      competitiveAdvantages: "• Strong brand recognition\n• Excellent customer relationships\n• Strategic location\n• Proven systems",
      growthOpportunities: "Expansion into adjacent markets, digital marketing opportunities, additional service offerings",
      reasonForSale: "Owner retirement - well-established business ready for new leadership"
    };

    if (suggestions[field]) {
      handleInputChange(field, suggestions[field]);
    }
  }, [handleInputChange]);

  const handleSaveDraft = useCallback(async () => {
    setIsSubmitting(true);
    // Simulate save
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSubmitting(false);
    alert('Draft saved successfully!');
  }, []);

  const handleSaveAndView = useCallback(async () => {
    setIsSubmitting(true);
    // Simulate save and view
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSubmitting(false);
    alert('Listing created successfully!');
  }, []);

  const progress = calculateProgress();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Hero Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                  <Building2 className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-3 h-3 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Create Your Business Listing
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
              Showcase your business to qualified buyers with our intelligent listing builder
            </p>
            
            {/* Progress Overview */}
            <div className="max-w-md mx-auto">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Progress
                </span>
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                  {progress}% Complete
                </span>
              </div>
              <Progress value={progress} className="h-3 bg-white/50 dark:bg-gray-800/50" />
              <div className="flex items-center justify-center mt-3 text-sm text-gray-600 dark:text-gray-400">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                {completedSections.size} of {sections.length} sections completed
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-6 pb-12">
        <div className="space-y-6">
          {sections.map((section, index) => {
            const Icon = section.icon;
            const isExpanded = expandedSections.has(section.id);
            const isCompleted = completedSections.has(section.id);
            
            return (
              <Card 
                key={section.id} 
                className={cn(
                  "transition-all duration-300 border-0 shadow-lg hover:shadow-xl",
                  isExpanded && "ring-2 ring-blue-200 dark:ring-blue-800",
                  isCompleted && "bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20"
                )}
              >
                <CardHeader 
                  className="cursor-pointer hover:bg-gray-50/50 dark:hover:bg-gray-800/50 transition-colors"
                  onClick={() => toggleSection(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={cn(
                        "w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-r shadow-lg",
                        section.gradientFrom,
                        section.gradientTo
                      )}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-3">
                          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
                            {section.title}
                          </CardTitle>
                          {isCompleted && (
                            <div className="flex items-center gap-1">
                              <CheckCircle className="w-5 h-5 text-green-500" />
                              <Badge variant="secondary" className="bg-green-100 text-green-800 border-0">
                                Complete
                              </Badge>
                            </div>
                          )}
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 mt-1">
                          {section.subtitle}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-500">
                          {section.fields.filter(field => formData[field] && formData[field] !== '').length} / {section.fields.length}
                        </div>
                        <div className="text-xs text-gray-400">fields completed</div>
                      </div>
                      {isExpanded ? (
                        <ChevronUp className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                </CardHeader>

                {isExpanded && (
                  <CardContent className="pt-0 animate-in slide-in-from-top-2 duration-200">
                    <div className="space-y-6">
                      {/* Section-specific content */}
                      {section.id === 'essentials' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Building2 className="w-4 h-4" />
                              Business Name <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              value={formData.businessName}
                              onChange={(e) => handleInputChange('businessName', e.target.value)}
                              placeholder="Enter your business name"
                              className="h-12 text-base border-2 focus:border-blue-500 transition-colors"
                              onFocus={() => setFocusedField('businessName')}
                              onBlur={() => setFocusedField(null)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Briefcase className="w-4 h-4" />
                              Industry <span className="text-red-500">*</span>
                            </Label>
                            <Select
                              value={formData.industry}
                              onValueChange={(value) => handleInputChange('industry', value)}
                            >
                              <SelectTrigger className="h-12 text-base border-2">
                                <SelectValue placeholder="Select your industry" />
                              </SelectTrigger>
                              <SelectContent>
                                {industries.map(industry => (
                                  <SelectItem key={industry} value={industry.toLowerCase()}>
                                    {industry}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <MapPin className="w-4 h-4" />
                              Location
                            </Label>
                            <Input
                              value={formData.location}
                              onChange={(e) => handleInputChange('location', e.target.value)}
                              placeholder="City, State"
                              className="h-12 text-base border-2 focus:border-blue-500"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Calendar className="w-4 h-4" />
                              Year Established
                            </Label>
                            <Input
                              type="number"
                              value={formData.yearEstablished}
                              onChange={(e) => handleInputChange('yearEstablished', e.target.value)}
                              placeholder="2020"
                              className="h-12 text-base border-2 focus:border-blue-500"
                              min="1800"
                              max="2025"
                            />
                          </div>

                          <div className="space-y-2 md:col-span-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Target className="w-4 h-4" />
                              Listing Status <span className="text-red-500">*</span>
                            </Label>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                              {statuses.map(status => (
                                <button
                                  key={status.value}
                                  type="button"
                                  onClick={() => handleInputChange('status', status.value)}
                                  className={cn(
                                    "p-3 rounded-lg border-2 transition-all text-left",
                                    formData.status === status.value
                                      ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20"
                                      : "border-gray-200 hover:border-gray-300"
                                  )}
                                >
                                  <div className={cn("text-xs px-2 py-1 rounded mb-1 w-fit", status.color)}>
                                    {status.label}
                                  </div>
                                  <div className="text-xs text-gray-600 dark:text-gray-400">
                                    {status.value === 'active' && 'Visible to all buyers'}
                                    {status.value === 'draft' && 'Save for later'}
                                    {status.value === 'confidential' && 'Requires NDA'}
                                  </div>
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {section.id === 'financials' && (
                        <div className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <DollarSign className="w-4 h-4" />
                                Asking Price <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                value={formData.askingPrice}
                                onChange={(e) => handleCurrencyChange('askingPrice', e.target.value)}
                                placeholder="$500,000"
                                className="h-12 border-2 focus:border-emerald-500 text-lg font-semibold"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <TrendingUp className="w-4 h-4" />
                                Cash Flow/SDE <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                value={formData.cashFlow}
                                onChange={(e) => handleCurrencyChange('cashFlow', e.target.value)}
                                placeholder="$75,000"
                                className="h-12 border-2 focus:border-emerald-500 text-lg font-semibold"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <PieChart className="w-4 h-4" />
                                Annual Revenue
                              </Label>
                              <Input
                                value={formData.annualRevenue}
                                onChange={(e) => handleCurrencyChange('annualRevenue', e.target.value)}
                                placeholder="$300,000"
                                className="h-12 text-base border-2 focus:border-emerald-500"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <BarChart3 className="w-4 h-4" />
                                2023 Revenue
                              </Label>
                              <Input
                                value={formData.revenue2023}
                                onChange={(e) => handleCurrencyChange('revenue2023', e.target.value)}
                                placeholder="$285,000"
                                className="h-12 text-base border-2 focus:border-emerald-500"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <TrendingUp className="w-4 h-4" />
                                2023 EBITDA
                              </Label>
                              <Input
                                value={formData.ebitda2023}
                                onChange={(e) => handleCurrencyChange('ebitda2023', e.target.value)}
                                placeholder="$85,000"
                                className="h-12 text-base border-2 focus:border-emerald-500"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <Building2 className="w-4 h-4" />
                                Inventory Value
                              </Label>
                              <Input
                                value={formData.inventoryValue}
                                onChange={(e) => handleCurrencyChange('inventoryValue', e.target.value)}
                                placeholder="$25,000"
                                className="h-12 text-base border-2 focus:border-emerald-500"
                              />
                            </div>
                          </div>

                          {/* Financial Summary Card */}
                          {(formData.askingPrice || formData.cashFlow) && (
                            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20 p-6 rounded-xl border border-emerald-200 dark:border-emerald-800">
                              <h4 className="font-semibold text-emerald-900 dark:text-emerald-100 mb-3 flex items-center gap-2">
                                <Zap className="w-5 h-5" />
                                Quick Financial Overview
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                {formData.askingPrice && (
                                  <div>
                                    <div className="text-emerald-700 dark:text-emerald-300">Asking Price</div>
                                    <div className="text-xl font-bold text-emerald-900 dark:text-emerald-100">
                                      {formData.askingPrice}
                                    </div>
                                  </div>
                                )}
                                {formData.cashFlow && (
                                  <div>
                                    <div className="text-emerald-700 dark:text-emerald-300">Cash Flow</div>
                                    <div className="text-xl font-bold text-emerald-900 dark:text-emerald-100">
                                      {formData.cashFlow}
                                    </div>
                                  </div>
                                )}
                                {formData.askingPrice && formData.cashFlow && (
                                  <div>
                                    <div className="text-emerald-700 dark:text-emerald-300">Multiple</div>
                                    <div className="text-xl font-bold text-emerald-900 dark:text-emerald-100">
                                      {(parseFloat(formData.askingPrice.replace(/[^\d]/g, '')) / parseFloat(formData.cashFlow.replace(/[^\d]/g, ''))).toFixed(1)}x
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {section.id === 'operations' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Users className="w-4 h-4" />
                              Number of Employees
                            </Label>
                            <Input
                              type="number"
                              value={formData.employees}
                              onChange={(e) => handleInputChange('employees', e.target.value)}
                              placeholder="5"
                              className="h-12 text-base border-2 focus:border-purple-500"
                              min="0"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Clock className="w-4 h-4" />
                              Owner Hours per Week
                            </Label>
                            <Input
                              type="number"
                              value={formData.ownerHours}
                              onChange={(e) => handleInputChange('ownerHours', e.target.value)}
                              placeholder="40"
                              className="h-12 text-base border-2 focus:border-purple-500"
                              min="0"
                              max="168"
                            />
                          </div>

                          <div className="space-y-2 md:col-span-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Briefcase className="w-4 h-4" />
                              Business Model
                            </Label>
                            <div className="flex items-end gap-2">
                              <Textarea
                                value={formData.businessModel}
                                onChange={(e) => handleInputChange('businessModel', e.target.value)}
                                placeholder="Describe how your business operates and generates revenue..."
                                rows={3}
                                className="text-base border-2 focus:border-purple-500 resize-none"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => handleAiGenerate('businessModel')}
                                className="shrink-0 h-10"
                              >
                                <Sparkles className="w-4 h-4 mr-2" />
                                Generate
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Home className="w-4 h-4" />
                              Real Estate Status
                            </Label>
                            <Select
                              value={formData.realEstateStatus}
                              onValueChange={(value) => handleInputChange('realEstateStatus', value)}
                            >
                              <SelectTrigger className="h-12 text-base border-2">
                                <SelectValue placeholder="Select real estate status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="owned">Owned</SelectItem>
                                <SelectItem value="leased">Leased</SelectItem>
                                <SelectItem value="available-separately">Available separately</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}

                      {section.id === 'story' && (
                        <div className="space-y-6">
                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <FileText className="w-4 h-4" />
                              Business Description
                            </Label>
                            <div className="flex items-end gap-2">
                              <Textarea
                                value={formData.businessDescription}
                                onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                                placeholder="Provide a detailed overview of the business, operations, history, and notable highlights..."
                                rows={6}
                                className="text-base border-2 focus:border-amber-500 resize-none"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => handleAiGenerate('businessDescription')}
                                className="shrink-0 h-10"
                              >
                                <Sparkles className="w-4 h-4 mr-2" />
                                Generate
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Award className="w-4 h-4" />
                              Brief Description (for portfolio)
                            </Label>
                            <div className="flex items-end gap-2">
                              <Textarea
                                value={formData.briefDescription}
                                onChange={(e) => handleInputChange('briefDescription', e.target.value)}
                                placeholder="A concise summary that will appear in your portfolio..."
                                rows={3}
                                className="text-base border-2 focus:border-amber-500 resize-none"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => handleAiGenerate('briefDescription')}
                                className="shrink-0 h-10"
                              >
                                <Sparkles className="w-4 h-4 mr-2" />
                                Generate
                              </Button>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <Lightbulb className="w-4 h-4" />
                                Key Features
                              </Label>
                              <div className="flex items-end gap-2">
                                <Textarea
                                  value={formData.keyFeatures}
                                  onChange={(e) => handleInputChange('keyFeatures', e.target.value)}
                                  placeholder="List standout features (one per line)"
                                  rows={4}
                                  className="text-base border-2 focus:border-amber-500 resize-none"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleAiGenerate('keyFeatures')}
                                  className="shrink-0 h-10"
                                >
                                  <Sparkles className="w-4 h-4 mr-2" />
                                  Generate
                                </Button>
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <Target className="w-4 h-4" />
                                Competitive Advantages
                              </Label>
                              <div className="flex items-end gap-2">
                                <Textarea
                                  value={formData.competitiveAdvantages}
                                  onChange={(e) => handleInputChange('competitiveAdvantages', e.target.value)}
                                  placeholder="Describe your competitive edge (one per line)"
                                  rows={4}
                                  className="text-base border-2 focus:border-amber-500 resize-none"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleAiGenerate('competitiveAdvantages')}
                                  className="shrink-0 h-10"
                                >
                                  <Sparkles className="w-4 h-4 mr-2" />
                                  Generate
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {section.id === 'growth' && (
                        <div className="space-y-6">
                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <TrendingUp className="w-4 h-4" />
                              Growth Opportunities
                            </Label>
                            <div className="flex items-end gap-2">
                              <Textarea
                                value={formData.growthOpportunities}
                                onChange={(e) => handleInputChange('growthOpportunities', e.target.value)}
                                placeholder="Outline realistic growth initiatives (one per line)"
                                rows={4}
                                className="text-base border-2 focus:border-rose-500 resize-none"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => handleAiGenerate('growthOpportunities')}
                                className="shrink-0 h-10"
                              >
                                <Sparkles className="w-4 h-4 mr-2" />
                                Generate
                              </Button>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <HandHeart className="w-4 h-4" />
                                Reason for Sale
                              </Label>
                              <Textarea
                                value={formData.reasonForSale}
                                onChange={(e) => handleInputChange('reasonForSale', e.target.value)}
                                placeholder="Explain why the owner is selling"
                                rows={3}
                                className="text-base border-2 focus:border-rose-500 resize-none"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 font-medium">
                                <Users className="w-4 h-4" />
                                Customer Base
                              </Label>
                              <Textarea
                                value={formData.customerBase}
                                onChange={(e) => handleInputChange('customerBase', e.target.value)}
                                placeholder="Describe your typical customers or target market"
                                rows={3}
                                className="text-base border-2 focus:border-rose-500 resize-none"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="flex items-center gap-2 font-medium">
                              <Briefcase className="w-4 h-4" />
                              Supplier Relationships
                            </Label>
                            <Textarea
                              value={formData.supplierRelationships}
                              onChange={(e) => handleInputChange('supplierRelationships', e.target.value)}
                              placeholder="Key suppliers and nature of relationships"
                              rows={3}
                              className="text-base border-2 focus:border-rose-500 resize-none"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            )
          })}

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Draft
            </Button>
            <Button
              type="button"
              onClick={handleSaveAndView}
              disabled={isSubmitting}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <Eye className="w-4 h-4" />
              Create & View
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}