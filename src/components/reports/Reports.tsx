import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  FileText, 
  Calendar,
  Download,
  Filter,
  Building,
  Users,
  Package
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { KPICard } from '@/components/dashboard/kpi-card';

interface ReportsProps {
  className?: string;
}

export const ReportsComponent: React.FC<ReportsProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('30');

  // Mock data - in a real app, this would come from API
  const reportData = {
    overview: {
      totalListings: 45,
      activeListings: 23,
      underContract: 7,
      closedDeals: 15,
      totalCommissions: 245000,
      avgDaysOnMarket: 67,
      conversionRate: 33.3
    },
    listings: [
      {
        id: '1',
        businessName: 'Downtown Restaurant',
        industry: 'Restaurant',
        askingPrice: 450000,
        status: 'Active',
        daysListed: 45,
        views: 234,
        inquiries: 12
      },
      {
        id: '2',
        businessName: 'Auto Repair Shop',
        industry: 'Auto',
        askingPrice: 275000,
        status: 'Under Contract',
        daysListed: 23,
        views: 156,
        inquiries: 8
      },
      {
        id: '3',
        businessName: 'Retail Store',
        industry: 'Retail',
        askingPrice: 125000,
        status: 'Sold',
        daysListed: 89,
        views: 89,
        inquiries: 5
      }
    ],
    performance: {
      monthlyData: [
        { month: 'Jan', listings: 8, closed: 2, revenue: 45000 },
        { month: 'Feb', listings: 12, closed: 3, revenue: 67000 },
        { month: 'Mar', listings: 15, closed: 4, revenue: 89000 },
        { month: 'Apr', listings: 18, closed: 6, revenue: 123000 }
      ]
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Active':
        return 'default';
      case 'Under Contract':
        return 'secondary';
      case 'Sold':
        return 'outline';
      default:
        return 'default';
    }
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
            <p className="text-muted-foreground">
              Analyze your business performance and track key metrics.
            </p>
          </div>
          <div className="mt-4 flex gap-3 sm:mt-0">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="365">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Reports Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="listings" className="flex items-center space-x-2">
              <Building className="h-4 w-4" />
              <span>Listings</span>
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Performance</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* KPI Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <KPICard
                title="Total Listings"
                value={reportData.overview.totalListings}
                icon={<Building className="h-5 w-5" />}
              />
              <KPICard
                title="Active Listings"
                value={reportData.overview.activeListings}
                icon={<Package className="h-5 w-5" />}
                trend={{ value: 12, positive: true }}
              />
              <KPICard
                title="Under Contract"
                value={reportData.overview.underContract}
                icon={<FileText className="h-5 w-5" />}
              />
              <KPICard
                title="Closed Deals"
                value={reportData.overview.closedDeals}
                icon={<TrendingUp className="h-5 w-5" />}
                trend={{ value: 8, positive: true }}
              />
            </div>

            {/* Additional Metrics */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Commissions</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${reportData.overview.totalCommissions.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +15% from last period
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Days on Market</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{reportData.overview.avgDaysOnMarket}</div>
                  <p className="text-xs text-muted-foreground">
                    -5 days from last period
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{reportData.overview.conversionRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    +2.1% from last period
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Listings Tab */}
          <TabsContent value="listings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Listing Performance</CardTitle>
                <CardDescription>
                  Detailed performance metrics for your listings.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportData.listings.map((listing) => (
                    <div key={listing.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold">{listing.businessName}</h3>
                          <Badge variant={getStatusBadgeVariant(listing.status)}>
                            {listing.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{listing.industry}</p>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>{listing.daysListed} days listed</span>
                          <span>{listing.views} views</span>
                          <span>{listing.inquiries} inquiries</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold">
                          ${listing.askingPrice.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance</CardTitle>
                <CardDescription>
                  Track your monthly listing and revenue performance.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportData.performance.monthlyData.map((month) => (
                    <div key={month.month} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <h3 className="font-semibold">{month.month} 2024</h3>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>{month.listings} listings</span>
                          <span>{month.closed} closed</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold">
                          ${month.revenue.toLocaleString()}
                        </div>
                        <p className="text-sm text-muted-foreground">Revenue</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>
                  Key insights from your performance data.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2 text-green-800">
                      <TrendingUp className="h-4 w-4" />
                      <span className="font-medium">Strong Performance</span>
                    </div>
                    <p className="text-sm text-green-700 mt-1">
                      Your conversion rate has improved by 15% this quarter.
                    </p>
                  </div>
                  
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2 text-blue-800">
                      <BarChart3 className="h-4 w-4" />
                      <span className="font-medium">Market Insight</span>
                    </div>
                    <p className="text-sm text-blue-700 mt-1">
                      Food service businesses are showing higher demand this month.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};