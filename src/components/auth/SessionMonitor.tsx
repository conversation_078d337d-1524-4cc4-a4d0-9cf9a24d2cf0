import React, { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

/**
 * SessionMonitor component that watches for session changes and automatically
 * redirects users to the index page when their session becomes null.
 * 
 * This component runs globally and handles the case where a user's session
 * expires or becomes invalid while they're using the app.
 */
export const SessionMonitor: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading } = useAuth();
  const previousUserRef = useRef(user);
  const hasRedirectedRef = useRef(false);

  useEffect(() => {
    // Don't do anything while loading
    if (loading) {
      return;
    }

    const previousUser = previousUserRef.current;
    const currentUser = user;

    // Update the ref for next comparison
    previousUserRef.current = currentUser;

    // If we're already on the index page, don't redirect
    if (location.pathname === '/') {
      hasRedirectedRef.current = false;
      return;
    }

    // If user was previously authenticated but is now null (session expired/cleared)
    // and we haven't already redirected, navigate to index page
    if (previousUser && !currentUser && !hasRedirectedRef.current) {
      console.log('SessionMonitor: Session lost, redirecting to index page');
      hasRedirectedRef.current = true;
      
      // Navigate immediately with replace to avoid back button issues
      navigate('/', { replace: true });
    }

    // Reset redirect flag when user becomes authenticated again
    if (currentUser) {
      hasRedirectedRef.current = false;
    }
  }, [user, loading, navigate, location.pathname]);

  // This component doesn't render anything
  return null;
};

export default SessionMonitor;
