import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

/**
 * SessionTest Component
 * 
 * This component is used to test and verify the two-token authentication strategy.
 * It displays the current authentication state and provides controls to test
 * session restoration after page refresh.
 * 
 * Features tested:
 * - Access token stored in memory (React state)
 * - Refresh token stored in httpOnly cookies
 * - User data stored in memory (React state)
 * - Session restoration on page refresh
 * - Automatic token refresh
 */
export const SessionTest: React.FC = () => {
  const { 
    user, 
    profile, 
    workspace, 
    accessToken, 
    loading, 
    error, 
    refreshSession,
    signOut 
  } = useAuth();

  const handleRefreshSession = async () => {
    try {
      await refreshSession();
      console.log('Session refreshed successfully');
    } catch (error) {
      console.error('Session refresh failed:', error);
    }
  };

  const handlePageRefresh = () => {
    window.location.reload();
  };

  const getRefreshTokenFromCookie = () => {
    const cookies = document.cookie.split(';');
    const refreshTokenCookie = cookies.find(cookie => cookie.trim().startsWith('refresh_token='));
    return refreshTokenCookie ? refreshTokenCookie.split('=')[1] : null;
  };

  const refreshTokenExists = !!getRefreshTokenFromCookie();

  if (loading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Session Test</CardTitle>
          <CardDescription>Testing two-token authentication strategy</CardDescription>
        </CardHeader>
        <CardContent>
          <LoadingOverlay
            message="Loading authentication state..."
            overlay={false}
            className="p-8"
            source="SessionTest:Unknown:AuthStateCheck"
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Session Test - Two-Token Strategy</CardTitle>
        <CardDescription>
          Testing authentication state and session restoration
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Authentication Status */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Authentication Status</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <span>User Authenticated:</span>
              <Badge variant={user ? "default" : "secondary"}>
                {user ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Access Token (Memory):</span>
              <Badge variant={accessToken ? "default" : "secondary"}>
                {accessToken ? "Present" : "None"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Refresh Token (Cookie):</span>
              <Badge variant={refreshTokenExists ? "default" : "secondary"}>
                {refreshTokenExists ? "Present" : "None"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Profile Data:</span>
              <Badge variant={profile ? "default" : "secondary"}>
                {profile ? "Loaded" : "None"}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        {/* User Information */}
        {user && (
          <div>
            <h3 className="text-lg font-semibold mb-3">User Information</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Email:</strong> {user.email}</div>
              <div><strong>User ID:</strong> {user.id}</div>
              {profile && (
                <>
                  <div><strong>Name:</strong> {profile.firstName} {profile.lastName}</div>
                  <div><strong>Role:</strong> {profile.role}</div>
                </>
              )}
              {workspace && (
                <>
                  <div><strong>Workspace:</strong> {workspace.companyName}</div>
                  <div><strong>Company Type:</strong> {workspace.companyType}</div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Token Information */}
        {accessToken && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Token Information</h3>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Access Token (first 20 chars):</strong> 
                <code className="ml-2 bg-muted px-2 py-1 rounded">
                  {accessToken.substring(0, 20)}...
                </code>
              </div>
              <div className="text-xs text-muted-foreground">
                ✓ Access token is stored in React state (memory only)
              </div>
              <div className="text-xs text-muted-foreground">
                ✓ Refresh token is stored in httpOnly cookie
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div>
            <h3 className="text-lg font-semibold mb-3 text-destructive">Error</h3>
            <div className="bg-destructive/10 border border-destructive/20 rounded p-3">
              <p className="text-sm text-destructive">{error.message}</p>
            </div>
          </div>
        )}

        <Separator />

        {/* Test Controls */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Test Controls</h3>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={handleRefreshSession}
              variant="outline"
              disabled={!refreshTokenExists}
            >
              Test Token Refresh
            </Button>
            <Button 
              onClick={handlePageRefresh}
              variant="outline"
            >
              Test Page Refresh
            </Button>
            {user && (
              <Button 
                onClick={signOut}
                variant="destructive"
              >
                Sign Out
              </Button>
            )}
          </div>
          <div className="mt-3 text-xs text-muted-foreground space-y-1">
            <div>• <strong>Test Token Refresh:</strong> Manually refresh the access token using the refresh token</div>
            <div>• <strong>Test Page Refresh:</strong> Reload the page to test session restoration</div>
            <div>• <strong>Sign Out:</strong> Clear all tokens and user data</div>
          </div>
        </div>

        {/* Implementation Notes */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Implementation Notes</h3>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>✓ <strong>Two-Token Strategy:</strong> Access token in memory, refresh token in httpOnly cookie</div>
            <div>✓ <strong>User Data:</strong> All user, profile, and workspace data stored in React state (memory)</div>
            <div>✓ <strong>Session Restoration:</strong> On page refresh, uses refresh token to get new access token and user data</div>
            <div>✓ <strong>Security:</strong> Access token lost on page refresh, refresh token persists in secure cookie</div>
            <div>✓ <strong>No localStorage:</strong> No authentication data stored in localStorage</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
