import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import SessionMonitor from '../SessionMonitor';
import { AuthCombinedProvider } from '@/contexts/AuthContext';

// Mock the useAuth hook
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/dashboard' }),
  };
});

// Mock the auth context
const mockUseAuth = vi.fn();
vi.mock('@/contexts/AuthContext', async () => {
  const actual = await vi.importActual('@/contexts/AuthContext');
  return {
    ...actual,
    useAuth: () => mockUseAuth(),
  };
});

describe('SessionMonitor', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    mockNavigate.mockClear();
    mockUseAuth.mockClear();
  });

  const renderSessionMonitor = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <SessionMonitor />
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  it('should not redirect when user is authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      loading: false,
    });

    renderSessionMonitor();

    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('should not redirect when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: true,
    });

    renderSessionMonitor();

    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('should redirect when user becomes null after being authenticated', async () => {
    // First render with authenticated user
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      loading: false,
    });

    const { rerender } = renderSessionMonitor();

    // Then simulate session loss
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
    });

    rerender(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <SessionMonitor />
        </BrowserRouter>
      </QueryClientProvider>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });

  it('should not redirect if user was never authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
    });

    renderSessionMonitor();

    expect(mockNavigate).not.toHaveBeenCalled();
  });
});
