import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { SimplePasswordField } from '@/components/ui/password-field';
import { Building2, AlertCircle } from 'lucide-react';
import { getWorkspaceRoute } from '@/lib/workspace-routing';
import { AuthErrorCode } from '@/types';
import { ButtonSpinner, LoadingOverlay } from '@/components/ui/loading-overlay';
import { ForgotPasswordModal } from '@/components/auth/ForgotPasswordForm';

interface SignInFormProps {
  redirectTo?: string;
  onSuccess?: () => void;
}

export const SignInForm: React.FC<SignInFormProps> = ({ redirectTo, onSuccess }) => {
  const { signIn, loading, error, clearError, user, workspace, profile } = useAuth();
  const navigate = useNavigate();
  
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(true);
  
  // Authentication flow state
  const [authStep, setAuthStep] = useState<'credentials' | 'authenticating'>('credentials');

  // Modal state
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  // Handle successful authentication and workspace routing
  useEffect(() => {
    // Navigate only after post-auth hydration completes (global loading false)
    if (user && authStep === 'authenticating' && !loading) {
      handleAuthenticationSuccess();
    }
  }, [user, authStep, loading]);

  const handleAuthenticationSuccess = () => {
    if (onSuccess) {
      onSuccess();
    } else {
      // Navigate to dashboard on successful authentication
      const destination = redirectTo || '/dashboard';
      navigate(destination, { replace: true });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    try {
      setAuthStep('authenticating');
      await signIn(email, password);
      // Success handling is done in useEffect
    } catch (error) {
      console.error('Sign in failed:', error);
      setAuthStep('credentials');
    }
  };

  const getErrorMessage = (error: any) => {
    if (!error) return null;

    switch (error.code) {
      case AuthErrorCode.INVALID_CREDENTIALS:
        return 'Invalid email or password. Please check your credentials and try again.';
      case AuthErrorCode.EMAIL_NOT_VERIFIED:
        return 'Please verify your email address before signing in. Check your inbox for a verification link.';
      case AuthErrorCode.ACCOUNT_LOCKED:
        return 'Your account has been temporarily locked. Please contact support for assistance.';
      case AuthErrorCode.NETWORK_ERROR:
        return 'Network connection error. Please check your internet connection and try again.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  };



  const isFormValid = () => {
    return email && 
           password && 
           !loading;
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Sign In
        </CardTitle>
        <CardDescription>
          Enter your credentials to access your workspace
        </CardDescription>
      </CardHeader>
      <CardContent className="relative">
        {/* Form loading overlay - only shows inside the form */}
        {authStep === 'authenticating' && (
          <div className="absolute inset-0 z-10">
            <LoadingOverlay
              message="Signing you in..."
              overlay={false}
              className="absolute inset-0 rounded-lg"
            />
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {getErrorMessage(error)}
              </AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              disabled={loading || authStep === 'authenticating'}
              autoComplete="email"
            />
          </div>
          
          <SimplePasswordField
            id="password"
            label="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            required
            disabled={loading || authStep === 'authenticating'}
            autoComplete="current-password"
          />

          <div className="flex items-center space-x-2">
            <Checkbox
              id="remember-me"
              checked={rememberMe}
              onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              disabled={loading || authStep === 'authenticating'}
            />
            <Label 
              htmlFor="remember-me" 
              className="text-sm font-normal cursor-pointer"
            >
              Remember me
            </Label>
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={!isFormValid()}
          >
            {authStep === 'authenticating' ? (
              <>
                <ButtonSpinner className="h-4 w-4 mr-2" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </Button>

          <div className="text-center">
            <Button
              type="button"
              variant="link"
              className="text-sm text-muted-foreground"
              onClick={() => setShowForgotPassword(true)}
              disabled={loading || authStep === 'authenticating'}
            >
              Forgot your password?
            </Button>
          </div>
        </form>
      </CardContent>
      <ForgotPasswordModal 
        open={showForgotPassword} 
        onOpenChange={setShowForgotPassword}
      />
    </Card>
  );
};