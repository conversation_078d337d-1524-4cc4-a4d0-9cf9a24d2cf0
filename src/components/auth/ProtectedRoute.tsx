import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { UserRole, Permission, WorkspaceStatus } from '@/types';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle } from 'lucide-react';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  requireAllPermissions?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

/**
 * Component that protects routes based on authentication and authorization
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions = [],
  requireAllPermissions = false,
  redirectTo = '/',
  fallback,
}) => {
  const location = useLocation();
  const { user, profile, workspace, loading: authLoading } = useAuth();
  const {
    hasRoleOrAbove,
    hasExactRole,
    hasAnyPermission,
    hasAllPermissions,
    loading: permissionsLoading,
    error: permissionsError,
  } = usePermissions();

  // Show loading state while checking authentication and permissions
  if (authLoading || permissionsLoading) {
    return <LoadingOverlay message="Loading..." />;
  }

  // Redirect to sign in if not authenticated
  if (!user) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Allow rendering when workspace is still hydrating post sign-in
  // Only enforce workspace-dependent checks if a workspace exists

  // Check workspace status
  if (workspace && workspace.status === WorkspaceStatus.SUSPENDED) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            This workspace has been suspended. Please contact support for assistance.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check trial expiration
  if (workspace && workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
    const trialEndsAt = new Date(workspace.trial_ends_at);
    const now = new Date();
    if (now > trialEndsAt) {
      return (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Alert variant="destructive" className="max-w-md">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your trial period has expired. Please upgrade your subscription to continue.
            </AlertDescription>
          </Alert>
        </div>
      );
    }
  }

  // Show permissions error if exists
  if (permissionsError) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            {permissionsError.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check role-based access
  let hasRoleAccess = true;
  if (requiredRole) {
    hasRoleAccess = hasExactRole(requiredRole) || hasRoleOrAbove(requiredRole);
  }

  // Check permission-based access
  let hasPermissionAccess = true;
  if (requiredPermissions.length > 0) {
    hasPermissionAccess = requireAllPermissions
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);
  }

  // Grant access if both role and permission checks pass
  const hasAccess = hasRoleAccess && hasPermissionAccess;

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Redirect to unauthorized page for access denied scenarios
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

/**
 * Higher-order component for protecting routes
 */
export const withProtectedRoute = <P extends object>(
  Component: React.ComponentType<P>,
  protectionProps: Omit<ProtectedRouteProps, 'children'>
) => {
  return (props: P) => (
    <ProtectedRoute {...protectionProps}>
      <Component {...props} />
    </ProtectedRoute>
  );
};