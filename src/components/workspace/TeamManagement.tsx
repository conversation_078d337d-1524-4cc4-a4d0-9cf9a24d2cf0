import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Users,
  UserPlus,
  Mail,
  MoreHorizontal,
  Shield,
  ShieldCheck,
  Eye,
  Crown,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Trash2,
  RefreshCw,
  Send,
} from 'lucide-react';
import { useWorkspace } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';
import {
  UserRole,
  UserProfile,
  WorkspaceInvitation,
  InvitationData,
} from '@/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { RoleGuard } from '@/components/auth/RoleGuard';

// Validation schema for invitation form
const invitationSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.nativeEnum(UserRole).refine(
    (role) => role !== UserRole.OWNER,
    'Cannot invite users as owners'
  ),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  message: z.string().optional(),
});

type InvitationFormData = z.infer<typeof invitationSchema>;

interface TeamManagementProps {
  className?: string;
}

// Helper function to get role icon
const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case UserRole.OWNER:
      return <Crown className="h-4 w-4 text-yellow-600" />;
    case UserRole.ADMIN:
      return <ShieldCheck className="h-4 w-4 text-blue-600" />;
    case UserRole.MEMBER:
      return <Shield className="h-4 w-4 text-green-600" />;
    case UserRole.VIEWER:
      return <Eye className="h-4 w-4 text-gray-600" />;
    default:
      return <Users className="h-4 w-4 text-gray-600" />;
  }
};

// Helper function to get role color
const getRoleColor = (role: UserRole) => {
  switch (role) {
    case UserRole.OWNER:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case UserRole.ADMIN:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case UserRole.MEMBER:
      return 'bg-green-100 text-green-800 border-green-200';
    case UserRole.VIEWER:
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Helper function to get invitation status
const getInvitationStatus = (invitation: WorkspaceInvitation) => {
  const now = new Date();
  const expiresAt = new Date(invitation.expires_at);
  
  if (invitation.accepted_at) {
    return { status: 'accepted', label: 'Accepted', icon: CheckCircle, color: 'text-green-600' };
  } else if (now > expiresAt) {
    return { status: 'expired', label: 'Expired', icon: XCircle, color: 'text-red-600' };
  } else {
    return { status: 'pending', label: 'Pending', icon: Clock, color: 'text-yellow-600' };
  }
};

export const TeamManagement: React.FC<TeamManagementProps> = ({ className }) => {
  const { user } = useAuth();
  const {
    workspace,
    teamMembers,
    invitations,
    loading,
    error,
    inviteTeamMember,
    removeTeamMember,
    updateMemberRole,
    resendInvitation,
    revokeInvitation,
    refreshTeamData,
    clearError,
  } = useWorkspace();
  const { canManageTeam, canInviteMembers, canRemoveMembers, canAssignRoles } = usePermissions();

  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<UserProfile | null>(null);
  const [memberToUpdateRole, setMemberToUpdateRole] = useState<UserProfile | null>(null);
  const [newRole, setNewRole] = useState<UserRole | null>(null);

  // Invitation form
  const invitationForm = useForm<InvitationFormData>({
    resolver: zodResolver(invitationSchema),
    defaultValues: {
      email: '',
      role: UserRole.MEMBER,
      first_name: '',
      last_name: '',
      message: '',
    },
  });

  // Check permissions
  if (!canManageTeam()) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You don't have permission to manage team members.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle invitation form submission
  const onInviteSubmit = useCallback(async (data: InvitationFormData) => {
    try {
      const redirectTo = window.location.origin;
      await inviteTeamMember(data.email, data.role, redirectTo);
      setIsInviteDialogOpen(false);
      invitationForm.reset();
    } catch (error) {
      console.error('Failed to send invitation:', error);
      // Keep dialog open on error so user can see the error and try again
      // The error will be displayed in the error section above
    }
  }, [inviteTeamMember, invitationForm]);

  // Handle member removal
  const handleRemoveMember = useCallback(async (member: UserProfile) => {
    try {
      await removeTeamMember(member.id);
      setMemberToRemove(null);
    } catch (error) {
      console.error('Failed to remove team member:', error);
    }
  }, [removeTeamMember]);

  // Handle role update
  const handleUpdateRole = useCallback(async (member: UserProfile, role: UserRole) => {
    try {
      await updateMemberRole(member.id, role);
      setMemberToUpdateRole(null);
      setNewRole(null);
    } catch (error) {
      console.error('Failed to update member role:', error);
    }
  }, [updateMemberRole]);

  // Handle invitation resend
  const handleResendInvitation = useCallback(async (invitationId: string) => {
    try {
      await resendInvitation(invitationId);
    } catch (error) {
      console.error('Failed to resend invitation:', error);
    }
  }, [resendInvitation]);

  // Handle invitation revoke
  const handleRevokeInvitation = useCallback(async (invitationId: string) => {
    try {
      await revokeInvitation(invitationId);
    } catch (error) {
      console.error('Failed to revoke invitation:', error);
    }
  }, [revokeInvitation]);

  // Get user initials for avatar
  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Team Management</h2>
            <p className="text-muted-foreground">
              Manage team members, roles, and invitations for your workspace.
            </p>
          </div>
          <RoleGuard allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}>
            <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4" />
                  Invite Member
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Invite Team Member</DialogTitle>
                  <DialogDescription>
                    Send an invitation to join your workspace. They'll receive an email with instructions to accept.
                  </DialogDescription>
                </DialogHeader>
                <Form {...invitationForm}>
                  <form onSubmit={invitationForm.handleSubmit(onInviteSubmit)} className="space-y-4">
                    <FormField
                      control={invitationForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter email address" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={invitationForm.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Role</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value={UserRole.ADMIN}>
                                <div className="flex items-center space-x-2">
                                  <ShieldCheck className="h-4 w-4 text-blue-600" />
                                  <span>Admin</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={UserRole.MEMBER}>
                                <div className="flex items-center space-x-2">
                                  <Shield className="h-4 w-4 text-green-600" />
                                  <span>Member</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={UserRole.VIEWER}>
                                <div className="flex items-center space-x-2">
                                  <Eye className="h-4 w-4 text-gray-600" />
                                  <span>Viewer</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose the role for this team member. You can change this later.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={invitationForm.control}
                        name="first_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="First name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={invitationForm.control}
                        name="last_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="Last name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <DialogFooter>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsInviteDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send Invitation
                          </>
                        )}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </RoleGuard>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm font-medium">{error.message}</span>
                </div>
                <Button variant="outline" size="sm" onClick={clearError}>
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Team Members */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>Team Members ({teamMembers.length})</span>
                </CardTitle>
                <CardDescription>
                  Active team members in your workspace
                </CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshTeamData}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {teamMembers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Team Members</h3>
                <p className="text-muted-foreground mb-4">
                  Start building your team by inviting members to your workspace.
                </p>
                <RoleGuard allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}>
                  <Button onClick={() => setIsInviteDialogOpen(true)}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite First Member
                  </Button>
                </RoleGuard>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Member</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {teamMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.avatar_url} alt={`${member.first_name} ${member.last_name}`} />
                            <AvatarFallback>
                              {getUserInitials(member.first_name, member.last_name)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {member.first_name} {member.last_name}
                              {member.id === user?.id && (
                                <Badge variant="outline" className="ml-2 text-xs">
                                  You
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {member.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getRoleColor(member.role)} border`}>
                          <div className="flex items-center space-x-1">
                            {getRoleIcon(member.role)}
                            <span className="capitalize">{member.role}</span>
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={member.is_active ? 'success' : 'secondary'}>
                          {member.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {member.joined_at ? formatDate(member.joined_at) : 'N/A'}
                      </TableCell>
                      <TableCell className="text-right">
                        <RoleGuard allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                disabled={member.id === user?.id}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              {canAssignRoles() && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setMemberToUpdateRole(member);
                                      setNewRole(UserRole.ADMIN);
                                    }}
                                    disabled={member.role === UserRole.ADMIN}
                                  >
                                    <ShieldCheck className="h-4 w-4 mr-2" />
                                    Make Admin
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setMemberToUpdateRole(member);
                                      setNewRole(UserRole.MEMBER);
                                    }}
                                    disabled={member.role === UserRole.MEMBER}
                                  >
                                    <Shield className="h-4 w-4 mr-2" />
                                    Make Member
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setMemberToUpdateRole(member);
                                      setNewRole(UserRole.VIEWER);
                                    }}
                                    disabled={member.role === UserRole.VIEWER}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    Make Viewer
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                </>
                              )}
                              {canRemoveMembers() && (
                                <DropdownMenuItem
                                  onClick={() => setMemberToRemove(member)}
                                  className="text-destructive focus:text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Remove Member
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </RoleGuard>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Pending Invitations */}
        {invitations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mail className="h-5 w-5" />
                <span>Pending Invitations ({invitations.length})</span>
              </CardTitle>
              <CardDescription>
                Invitations that haven't been accepted yet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sent</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invitations.map((invitation) => {
                    const status = getInvitationStatus(invitation);
                    const StatusIcon = status.icon;
                    
                    return (
                      <TableRow key={invitation.id}>
                        <TableCell className="font-medium">
                          {invitation.email}
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getRoleColor(invitation.role)} border`}>
                            <div className="flex items-center space-x-1">
                              {getRoleIcon(invitation.role)}
                              <span className="capitalize">{invitation.role}</span>
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <StatusIcon className={`h-4 w-4 ${status.color}`} />
                            <span className={status.color}>{status.label}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {formatDate(invitation.created_at)}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {formatDate(invitation.expires_at)}
                        </TableCell>
                        <TableCell className="text-right">
                          <RoleGuard allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                {status.status === 'pending' && (
                                  <DropdownMenuItem
                                    onClick={() => handleResendInvitation(invitation.id)}
                                  >
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                    Resend Invitation
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() => handleRevokeInvitation(invitation.id)}
                                  className="text-destructive focus:text-destructive"
                                >
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Revoke Invitation
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </RoleGuard>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Remove Member Confirmation Dialog */}
        <AlertDialog open={!!memberToRemove} onOpenChange={() => setMemberToRemove(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove{' '}
                <strong>
                  {memberToRemove?.first_name} {memberToRemove?.last_name}
                </strong>{' '}
                from your workspace? This action cannot be undone and they will lose access to all workspace data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => memberToRemove && handleRemoveMember(memberToRemove)}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Remove Member
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Update Role Confirmation Dialog */}
        <AlertDialog
          open={!!memberToUpdateRole && !!newRole}
          onOpenChange={() => {
            setMemberToUpdateRole(null);
            setNewRole(null);
          }}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Update Member Role</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to change{' '}
                <strong>
                  {memberToUpdateRole?.first_name} {memberToUpdateRole?.last_name}
                </strong>
                's role from{' '}
                <strong className="capitalize">{memberToUpdateRole?.role}</strong> to{' '}
                <strong className="capitalize">{newRole}</strong>?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() =>
                  memberToUpdateRole && newRole && handleUpdateRole(memberToUpdateRole, newRole)
                }
              >
                Update Role
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};