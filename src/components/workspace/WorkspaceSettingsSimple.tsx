import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Save, 
  AlertCircle, 
  CreditCard, 
  Users, 
  Palette, 
  Building2,
  Upload,
  X
} from 'lucide-react';
import { useWorkspace } from '@/contexts/AuthContext';
import { ButtonSpinner } from '@/components/ui/loading-overlay';
import { usePermissions } from '@/hooks/usePermissions';
import { 
  CompanyType, 
  SubscriptionPlan, 
  WorkspaceStatus 
} from '@/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { TeamManagement } from './TeamManagement';
import { WorkspaceSwitcher } from './WorkspaceSwitcher';

// Validation schemas
const companyInfoSchema = z.object({
  company_name: z.string().min(1, 'Company name is required'),
  company_type: z.nativeEnum(CompanyType),
  address: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  license_number: z.string().optional(),
});

const brandingSchema = z.object({
  primary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  logo_url: z.string().optional(),
});

type CompanyInfoFormData = z.infer<typeof companyInfoSchema>;
type BrandingFormData = z.infer<typeof brandingSchema>;

interface WorkspaceSettingsProps {
  className?: string;
}

export const WorkspaceSettingsComponent: React.FC<WorkspaceSettingsProps> = ({ className }) => {
  const { workspace, updateWorkspace, loading, error } = useWorkspace();
  const { canViewWorkspaceSettings, canManageBilling } = usePermissions();
  const [activeTab, setActiveTab] = useState('company');
  const [isUploading, setIsUploading] = useState(false);

  // Company info form
  const companyForm = useForm<CompanyInfoFormData>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      company_name: workspace?.company_name || '',
      company_type: workspace?.company_type || CompanyType.TEAM,
      address: workspace?.address || '',
      phone: workspace?.phone || '',
      website: workspace?.website || '',
      license_number: workspace?.license_number || '',
    },
  });

  // Branding form
  const brandingForm = useForm<BrandingFormData>({
    resolver: zodResolver(brandingSchema),
    defaultValues: {
      primary_color: workspace?.primary_color || '#3B82F6',
      logo_url: workspace?.logo_url || '',
    },
  });

  // Check permissions
  if (!canViewWorkspaceSettings()) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You don't have permission to view workspace settings.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle form submissions
  const onCompanyInfoSubmit = async (data: CompanyInfoFormData) => {
    try {
      await updateWorkspace(data);
    } catch (error) {
      console.error('Failed to update company information:', error);
    }
  };

  const onBrandingSubmit = async (data: BrandingFormData) => {
    try {
      await updateWorkspace(data);
    } catch (error) {
      console.error('Failed to update branding:', error);
    }
  };

  if (!workspace) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Workspace</h3>
            <p className="text-muted-foreground">
              No workspace data available.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workspace Settings</h1>
          <p className="text-muted-foreground">
            Manage your workspace configuration, branding, and team.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">{error.message}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Settings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="company" className="flex items-center space-x-2">
              <Building2 className="h-4 w-4" />
              <span>Company</span>
            </TabsTrigger>
            <TabsTrigger value="branding" className="flex items-center space-x-2">
              <Palette className="h-4 w-4" />
              <span>Branding</span>
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Team</span>
            </TabsTrigger>
            <TabsTrigger value="workspace" className="flex items-center space-x-2">
              <Building2 className="h-4 w-4" />
              <span>Workspace</span>
            </TabsTrigger>
            <TabsTrigger 
              value="billing" 
              className="flex items-center space-x-2"
              disabled={!canManageBilling()}
            >
              <CreditCard className="h-4 w-4" />
              <span>Billing</span>
            </TabsTrigger>
          </TabsList>

          {/* Company Information Tab */}
          <TabsContent value="company" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
                <CardDescription>
                  Update your company details and business information.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...companyForm}>
                  <form onSubmit={companyForm.handleSubmit(onCompanyInfoSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={companyForm.control}
                        name="company_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter company name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="company_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select company type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value={CompanyType.INDIVIDUAL}>Individual</SelectItem>
                                <SelectItem value={CompanyType.TEAM}>Team</SelectItem>
                                <SelectItem value={CompanyType.FIRM}>Firm</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Website</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="license_number"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>License Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter license number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={companyForm.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter company address"
                              className="min-h-[80px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <ButtonSpinner className="h-4 w-4 mr-2 text-white" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Branding Tab */}
          <TabsContent value="branding" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Branding & Customization</CardTitle>
                <CardDescription>
                  Customize your workspace appearance with your company branding.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...brandingForm}>
                  <form onSubmit={brandingForm.handleSubmit(onBrandingSubmit)} className="space-y-6">
                    {/* Logo Upload */}
                    <div className="space-y-4">
                      <Label>Company Logo</Label>
                      <div className="flex items-center space-x-4">
                        {workspace.logo_url && (
                          <div className="w-16 h-16 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
                            <img 
                              src={workspace.logo_url} 
                              alt="Company logo" 
                              className="w-full h-full object-contain"
                            />
                          </div>
                        )}
                        <div className="flex-1">
                          <Button
                            type="button"
                            variant="outline"
                            disabled={isUploading}
                          >
                            {isUploading ? (
                              <>
                                <ButtonSpinner className="h-4 w-4 mr-2" />
                                Uploading...
                              </>
                            ) : (
                              <>
                                <Upload className="h-4 w-4 mr-2" />
                                Upload Logo
                              </>
                            )}
                          </Button>
                          <p className="text-sm text-muted-foreground mt-1">
                            Recommended: Square image, max 2MB
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Primary Color */}
                    <FormField
                      control={brandingForm.control}
                      name="primary_color"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Color</FormLabel>
                          <div className="flex items-center space-x-4">
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="color"
                                  className="w-12 h-10 rounded border border-input cursor-pointer"
                                  {...field}
                                />
                                <Input
                                  placeholder="#3B82F6"
                                  className="w-32"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <div 
                              className="w-10 h-10 rounded border-2 border-gray-200"
                              style={{ backgroundColor: field.value }}
                            />
                          </div>
                          <FormDescription>
                            This color will be used for buttons, links, and accents throughout your workspace.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <ButtonSpinner className="h-4 w-4 mr-2 text-white" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team Management Tab */}
          <TabsContent value="team" className="space-y-6">
            <TeamManagement />
          </TabsContent>

          {/* Workspace Management Tab */}
          <TabsContent value="workspace" className="space-y-6">
            <WorkspaceSwitcher />
          </TabsContent>

          {/* Billing & Subscription Tab */}
          <TabsContent value="billing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Current Plan</CardTitle>
                <CardDescription>
                  Manage your subscription and billing information.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Plan Status */}
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold capitalize">
                          {workspace.subscription_plan} Plan
                        </h3>
                        <Badge 
                          variant={workspace.status === WorkspaceStatus.ACTIVE ? 'default' : 
                                 workspace.status === WorkspaceStatus.TRIAL ? 'secondary' : 'destructive'}
                        >
                          {workspace.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at && (
                          `Trial ends ${new Date(workspace.trial_ends_at).toLocaleDateString()}`
                        )}
                        {workspace.status === WorkspaceStatus.ACTIVE && (
                          'Your subscription is active'
                        )}
                        {workspace.status === WorkspaceStatus.SUSPENDED && (
                          'Your workspace has been suspended'
                        )}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">
                        {workspace.subscription_plan === SubscriptionPlan.TRIAL ? 'Free' :
                         workspace.subscription_plan === SubscriptionPlan.BASIC ? '$29' :
                         workspace.subscription_plan === SubscriptionPlan.PRO ? '$79' :
                         workspace.subscription_plan === SubscriptionPlan.ENTERPRISE ? '$199' : 'Free'}
                      </div>
                      {workspace.subscription_plan !== SubscriptionPlan.TRIAL && (
                        <div className="text-sm text-muted-foreground">per month</div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">
                      Manage Billing
                    </Button>
                    <Button>
                      Upgrade Plan
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};