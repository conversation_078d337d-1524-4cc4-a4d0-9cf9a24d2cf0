// import React, { useState } from 'react';
// import { Plus, Users, Mail, UserCheck, UserX, MoreHorizontal, Shield, Crown, Eye } from 'lucide-react';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
// import { useToast } from '@/hooks/use-toast';
// import { useWorkspace } from '@/contexts/WorkspaceContext';
// import { useAuth } from '@/contexts/AuthContext';
// import { UserRole, UserProfile, WorkspaceInvitation } from '@/types';
// import { cn } from '@/lib/utils';

// const TeamManagement: React.FC = () => {
//   const { toast } = useToast();
//   const { profile } = useAuth();
//   const { 
//     teamMembers, 
//     invitations, 
//     loading, 
//     inviteTeamMember, 
//     removeTeamMember, 
//     updateMemberRole, 
//     resendInvitation, 
//     revokeInvitation 
//   } = useWorkspace();

//   const [isInviting, setIsInviting] = useState(false);
//   const [inviteEmail, setInviteEmail] = useState('');
//   const [inviteRole, setInviteRole] = useState<UserRole>(UserRole.MEMBER);
//   const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

//   // Permission checks
//   const canManageTeam = profile?.role === 'owner' || profile?.role === 'admin';
//   const canInvite = canManageTeam;
//   const canRemove = canManageTeam;
//   const canUpdateRoles = canManageTeam;

//   const handleInviteMember = async () => {
//     if (!inviteEmail || !inviteRole) {
//       toast({
//         title: "Missing Information",
//         description: "Please provide both email and role.",
//         variant: "destructive"
//       });
//       return;
//     }

//     try {
//       setIsInviting(true);
//       const redirectTo = window.location.origin;
//       await inviteTeamMember(inviteEmail, inviteRole, redirectTo);
//       setInviteEmail('');
//       setInviteRole(UserRole.MEMBER);
//       setIsInviteModalOpen(false);
//       // toast({
//       //   title: "Invitation Sent",
//       //   description: `Invitation sent to ${inviteEmail}`,
//       // });
//     } catch (error) {
//       toast({
//         title: "Failed to Send Invitation",
//         description: error instanceof Error ? error.message : "Unknown error occurred",
//         variant: "destructive"
//       });
//     } finally {
//       setIsInviting(false);
//     }
//   };

//   const handleRemoveMember = async (member: UserProfile) => {
//     try {
//       await removeTeamMember(member.id);
//       toast({
//         title: "Member Removed",
//         description: `${member.first_name} ${member.last_name} has been removed from the team.`,
//       });
//     } catch (error) {
//       toast({
//         title: "Failed to Remove Member",
//         description: error instanceof Error ? error.message : "Unknown error occurred",
//         variant: "destructive"
//       });
//     }
//   };

//   const handleUpdateRole = async (memberId: string, newRole: UserRole) => {
//     try {
//       await updateMemberRole(memberId, newRole);
//       toast({
//         title: "Role Updated",
//         description: "Team member role has been updated successfully.",
//       });
//     } catch (error) {
//       toast({
//         title: "Failed to Update Role",
//         description: error instanceof Error ? error.message : "Unknown error occurred",
//         variant: "destructive"
//       });
//     }
//   };

//   const handleResendInvitation = async (invitation: WorkspaceInvitation) => {
//     try {
//       await resendInvitation(invitation.id);
//       toast({
//         title: "Invitation Resent",
//         description: `Invitation resent to ${invitation.email}`,
//       });
//     } catch (error) {
//       toast({
//         title: "Failed to Resend Invitation",
//         description: error instanceof Error ? error.message : "Unknown error occurred",
//         variant: "destructive"
//       });
//     }
//   };

//   const handleRevokeInvitation = async (invitation: WorkspaceInvitation) => {
//     try {
//       await revokeInvitation(invitation.id);
//       toast({
//         title: "Invitation Revoked",
//         description: `Invitation to ${invitation.email} has been revoked.`,
//       });
//     } catch (error) {
//       toast({
//         title: "Failed to Revoke Invitation",
//         description: error instanceof Error ? error.message : "Unknown error occurred",
//         variant: "destructive"
//       });
//     }
//   };

//   const getRoleIcon = (role: UserRole) => {
//     switch (role) {
//       case UserRole.OWNER:
//         return <Crown className="h-4 w-4" />;
//       case UserRole.ADMIN:
//         return <Shield className="h-4 w-4" />;
//       case UserRole.MEMBER:
//         return <UserCheck className="h-4 w-4" />;
//       case UserRole.VIEWER:
//         return <Eye className="h-4 w-4" />;
//       default:
//         return <UserCheck className="h-4 w-4" />;
//     }
//   };

//   const getRoleColor = (role: UserRole) => {
//     switch (role) {
//       case UserRole.OWNER:
//         return 'bg-purple-100 text-purple-800 border-purple-200';
//       case UserRole.ADMIN:
//         return 'bg-blue-100 text-blue-800 border-blue-200';
//       case UserRole.MEMBER:
//         return 'bg-green-100 text-green-800 border-green-200';
//       case UserRole.VIEWER:
//         return 'bg-gray-100 text-gray-800 border-gray-200';
//       default:
//         return 'bg-gray-100 text-gray-800 border-gray-200';
//     }
//   };

//   const getInitials = (firstName: string, lastName: string) => {
//     return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
//   };

//   const isInvitationExpired = (expiresAt: string) => {
//     return new Date(expiresAt) < new Date();
//   };

//   const TeamMemberCard: React.FC<{ member: UserProfile }> = ({ member }) => {
//     const isCurrentUser = member.id === profile?.id;
//     const canModifyMember = canRemove && !isCurrentUser && 
//       (profile?.role === 'owner' || (profile?.role === 'admin' && member.role !== 'owner' && member.role !== 'admin'));

//     return (
//       <Card className="p-4">
//         <div className="flex items-center justify-between">
//           <div className="flex items-center space-x-3">
//             <Avatar className="h-10 w-10">
//               <AvatarImage src={member.avatar_url || undefined} />
//               <AvatarFallback>{getInitials(member.first_name, member.last_name)}</AvatarFallback>
//             </Avatar>
//             <div>
//               <div className="flex items-center space-x-2">
//                 <h3 className="font-semibold text-sm">{member.first_name} {member.last_name}</h3>
//                 {isCurrentUser && <Badge variant="secondary" className="text-xs">You</Badge>}
//               </div>
//               <p className="text-sm text-muted-foreground">{member.email}</p>
//               {member.phone && (
//                 <p className="text-xs text-muted-foreground">{member.phone}</p>
//               )}
//             </div>
//           </div>
//           <div className="flex items-center space-x-2">
//             <Badge className={cn("flex items-center space-x-1", getRoleColor(member.role))}>
//               {getRoleIcon(member.role)}
//               <span className="capitalize">{member.role}</span>
//             </Badge>
//             {canModifyMember && (
//               <DropdownMenu>
//                 <DropdownMenuTrigger asChild>
//                   <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
//                     <MoreHorizontal className="h-4 w-4" />
//                   </Button>
//                 </DropdownMenuTrigger>
//                 <DropdownMenuContent align="end">
//                   {canUpdateRoles && (
//                     <>
//                       <DropdownMenuItem onClick={() => handleUpdateRole(member.id, UserRole.ADMIN)}>
//                         Promote to Admin
//                       </DropdownMenuItem>
//                       <DropdownMenuItem onClick={() => handleUpdateRole(member.id, UserRole.MEMBER)}>
//                         Set as Member
//                       </DropdownMenuItem>
//                       <DropdownMenuItem onClick={() => handleUpdateRole(member.id, UserRole.VIEWER)}>
//                         Set as Viewer
//                       </DropdownMenuItem>
//                       <DropdownMenuSeparator />
//                     </>
//                   )}
//                   <AlertDialog>
//                     <AlertDialogTrigger asChild>
//                       <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive">
//                         <UserX className="h-4 w-4 mr-2" />
//                         Remove Member
//                       </DropdownMenuItem>
//                     </AlertDialogTrigger>
//                     <AlertDialogContent>
//                       <AlertDialogHeader>
//                         <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
//                         <AlertDialogDescription>
//                           Are you sure you want to remove {member.first_name} {member.last_name} from the team? 
//                           This action cannot be undone and they will lose access to all workspace resources.
//                         </AlertDialogDescription>
//                       </AlertDialogHeader>
//                       <AlertDialogFooter>
//                         <AlertDialogCancel>Cancel</AlertDialogCancel>
//                         <AlertDialogAction 
//                           onClick={() => handleRemoveMember(member)}
//                           className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
//                         >
//                           Remove Member
//                         </AlertDialogAction>
//                       </AlertDialogFooter>
//                     </AlertDialogContent>
//                   </AlertDialog>
//                 </DropdownMenuContent>
//               </DropdownMenu>
//             )}
//           </div>
//         </div>
//         {member.specialties && member.specialties.length > 0 && (
//           <div className="mt-3">
//             <div className="flex flex-wrap gap-1">
//               {member.specialties.map((specialty, index) => (
//                 <Badge key={index} variant="outline" className="text-xs">
//                   {specialty}
//                 </Badge>
//               ))}
//             </div>
//           </div>
//         )}
//       </Card>
//     );
//   };

//   const InvitationCard: React.FC<{ invitation: WorkspaceInvitation }> = ({ invitation }) => {
//     const expired = isInvitationExpired(invitation.expires_at);

//     return (
//       <Card className="p-4">
//         <div className="flex items-center justify-between">
//           <div className="flex items-center space-x-3">
//             <Avatar className="h-10 w-10">
//               <AvatarFallback>
//                 <Mail className="h-5 w-5" />
//               </AvatarFallback>
//             </Avatar>
//             <div>
//               <h3 className="font-semibold text-sm">{invitation.email}</h3>
//               <p className="text-xs text-muted-foreground">
//                 Invited {new Date(invitation.created_at).toLocaleDateString()}
//               </p>
//               <p className="text-xs text-muted-foreground">
//                 Expires {new Date(invitation.expires_at).toLocaleDateString()}
//               </p>
//             </div>
//           </div>
//           <div className="flex items-center space-x-2">
//             <Badge className={cn("flex items-center space-x-1", getRoleColor(invitation.role))}>
//               {getRoleIcon(invitation.role)}
//               <span className="capitalize">{invitation.role}</span>
//             </Badge>
//             {expired && <Badge variant="destructive">Expired</Badge>}
//             <DropdownMenu>
//               <DropdownMenuTrigger asChild>
//                 <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
//                   <MoreHorizontal className="h-4 w-4" />
//                 </Button>
//               </DropdownMenuTrigger>
//               <DropdownMenuContent align="end">
//                 <DropdownMenuItem onClick={() => handleResendInvitation(invitation)}>
//                   <Mail className="h-4 w-4 mr-2" />
//                   Resend Invitation
//                 </DropdownMenuItem>
//                 <DropdownMenuSeparator />
//                 <AlertDialog>
//                   <AlertDialogTrigger asChild>
//                     <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive">
//                       <UserX className="h-4 w-4 mr-2" />
//                       Revoke Invitation
//                     </DropdownMenuItem>
//                   </AlertDialogTrigger>
//                   <AlertDialogContent>
//                     <AlertDialogHeader>
//                       <AlertDialogTitle>Revoke Invitation</AlertDialogTitle>
//                       <AlertDialogDescription>
//                         Are you sure you want to revoke the invitation for {invitation.email}? 
//                         They will no longer be able to join the workspace using this invitation.
//                       </AlertDialogDescription>
//                     </AlertDialogHeader>
//                     <AlertDialogFooter>
//                       <AlertDialogCancel>Cancel</AlertDialogCancel>
//                       <AlertDialogAction 
//                         onClick={() => handleRevokeInvitation(invitation)}
//                         className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
//                       >
//                         Revoke Invitation
//                       </AlertDialogAction>
//                     </AlertDialogFooter>
//                   </AlertDialogContent>
//                 </AlertDialog>
//               </DropdownMenuContent>
//             </DropdownMenu>
//           </div>
//         </div>
//       </Card>
//     );
//   };

//   return (
//     <div className="space-y-6">
//       <div className="flex items-center justify-between">
//         <div>
//           <h1 className="text-3xl font-bold">Team Management</h1>
//           <p className="text-muted-foreground">
//             Manage your workspace team members and invitations
//           </p>
//         </div>
//         {canInvite && (
//           <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
//             <DialogTrigger asChild>
//               <Button>
//                 <Plus className="h-4 w-4" />
//                 Invite Member
//               </Button>
//             </DialogTrigger>
//             <DialogContent>
//               <DialogHeader>
//                 <DialogTitle>Invite Team Member</DialogTitle>
//                 <DialogDescription>
//                   Send an invitation to join your workspace as a team member.
//                 </DialogDescription>
//               </DialogHeader>
//               <div className="space-y-4">
//                 <div>
//                   <Label htmlFor="email">Email Address</Label>
//                   <Input
//                     id="email"
//                     type="email"
//                     placeholder="<EMAIL>"
//                     value={inviteEmail}
//                     onChange={(e) => setInviteEmail(e.target.value)}
//                   />
//                 </div>
//                 <div>
//                   <Label htmlFor="role">Role</Label>
//                   <Select value={inviteRole} onValueChange={(value) => setInviteRole(value as UserRole)}>
//                     <SelectTrigger>
//                       <SelectValue />
//                     </SelectTrigger>
//                     <SelectContent>
//                       <SelectItem value={UserRole.ADMIN}>Admin</SelectItem>
//                       <SelectItem value={UserRole.MEMBER}>Member</SelectItem>
//                       <SelectItem value={UserRole.VIEWER}>Viewer</SelectItem>
//                     </SelectContent>
//                   </Select>
//                 </div>
//                 <Button 
//                   onClick={handleInviteMember} 
//                   disabled={isInviting || !inviteEmail}
//                   className="w-full"
//                 >
//                   {isInviting ? "Sending..." : "Send Invitation"}
//                 </Button>
//               </div>
//             </DialogContent>
//           </Dialog>
//         )}
//       </div>

//       <Tabs defaultValue="members" className="space-y-4">
//         <TabsList>
//           <TabsTrigger value="members" className="flex items-center space-x-2">
//             <Users className="h-4 w-4" />
//             <span>Team Members ({teamMembers.length})</span>
//           </TabsTrigger>
//           <TabsTrigger value="invitations" className="flex items-center space-x-2">
//             <Mail className="h-4 w-4" />
//             <span>Pending Invitations ({invitations.length})</span>
//           </TabsTrigger>
//         </TabsList>

//         <TabsContent value="members">
//           <Card>
//             <CardHeader>
//               <CardTitle>Team Members</CardTitle>
//               <CardDescription>
//                 All active members of your workspace with their roles and permissions.
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               {loading ? (
//                 <div className="text-center py-8 text-muted-foreground">
//                   Loading team members...
//                 </div>
//               ) : teamMembers.length === 0 ? (
//                 <div className="text-center py-8 text-muted-foreground">
//                   No team members found.
//                 </div>
//               ) : (
//                 teamMembers.map((member) => (
//                   <TeamMemberCard key={member.id} member={member} />
//                 ))
//               )}
//             </CardContent>
//           </Card>
//         </TabsContent>

//         <TabsContent value="invitations">
//           <Card>
//             <CardHeader>
//               <CardTitle>Pending Invitations</CardTitle>
//               <CardDescription>
//                 Invitations that have been sent but not yet accepted.
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               {loading ? (
//                 <div className="text-center py-8 text-muted-foreground">
//                   Loading invitations...
//                 </div>
//               ) : invitations.length === 0 ? (
//                 <div className="text-center py-8 text-muted-foreground">
//                   No pending invitations.
//                 </div>
//               ) : (
//                 invitations.map((invitation) => (
//                   <InvitationCard key={invitation.id} invitation={invitation} />
//                 ))
//               )}
//             </CardContent>
//           </Card>
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// };

// export default TeamManagement;