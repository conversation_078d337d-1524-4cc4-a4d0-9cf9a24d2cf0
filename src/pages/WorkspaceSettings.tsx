import React from 'react';
import { WorkspaceSettingsComponent } from '@/components/workspace/WorkspaceSettingsSimple';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { UserRole } from '@/types';

const WorkspaceSettingsPage: React.FC = () => {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="container mx-auto py-6 px-4">
        <WorkspaceSettingsComponent />
      </div>
    </ProtectedRoute>
  );
};

export default WorkspaceSettingsPage;