import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
// import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { LoadingOverlay } from '@/components/ui/loading-overlay';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { apiClient } from '@/lib/api-client';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, profile, workspace } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        setLoading(true);
        setError(null);

        // // Handle the auth callback
        // // const { data, error: authError } = await supabase.auth.getSession();
        // const { data, success, message } = await apiClient.getSession();

        // if (!success) {
        //   throw new Error(message);
        // }

        // if (!data.session?.user) {
        //   throw new Error('No user session found');
        // }

        // const user = data.session.user;

        // // Check if this is a new user who needs workspace setup
        // const { data: existingProfile } = await supabase
        //   .from('user_profiles')
        //   .select('*')
        //   .eq('id', user.id)
        //   .single();

        // if (!existingProfile) {
        //   // This is a new user, create workspace and profile from metadata
        //   const metadata = user.user_metadata;
          
        //   if (!metadata.company_name || !metadata.first_name || !metadata.last_name) {
        //     throw new Error('Missing required user information');
        //   }

        //   // Create workspace
        //   const { data: workspaceData, error: workspaceError } = await supabase
        //     .from('workspaces')
        //     .insert({
        //       company_name: metadata.company_name,
        //       company_type: metadata.company_type || 'team',
        //       address: metadata.address || null,
        //       website: metadata.website || null,
        //       phone: metadata.phone || null,
        //       status: 'trial',
        //       subscription_plan: 'trial',
        //       primary_color: '#3B82F6',
        //       specialties: [],
        //       target_markets: [],
        //       onboarding_completed: false,
        //       onboarding_step: 1,
        //       trial_ends_at: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        //     })
        //     .select()
        //     .single();

        //   if (workspaceError) {
        //     throw new Error(`Failed to create workspace: ${workspaceError.message}`);
        //   }

        //   // Create user profile
        //   const { error: profileError } = await supabase
        //     .from('user_profiles')
        //     .insert({
        //       id: user.id,
        //       workspace_id: workspaceData.id,
        //       email: user.email!,
        //       first_name: metadata.first_name,
        //       last_name: metadata.last_name,
        //       role: 'owner',
        //       phone: metadata.phone || null,
        //       license_number: metadata.license_number || null,
        //       specialties: [],
        //       is_active: true,
        //       joined_at: new Date().toISOString(),
        //     });

        //   if (profileError) {
        //     // Clean up workspace if profile creation fails
        //     await supabase.from('workspaces').delete().eq('id', workspaceData.id);
        //     throw new Error(`Failed to create user profile: ${profileError.message}`);
        //   }
        // }

        setSuccess(true);
        
        // Redirect to dashboard after a short delay
        // setTimeout(() => {
        //   navigate('/dashboard', { replace: true });
        // }, 2000);

      } catch (error) {
        console.error('Auth callback error:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  if (loading) {
    return (
      <LoadingOverlay
        message="Verifying your account..."
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-destructive">
              <XCircle className="h-6 w-6 mx-auto mb-2" />
              Verification Failed
            </CardTitle>
            <CardDescription className="text-center">
              There was a problem setting up your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
            
            <div className="flex flex-col space-y-2">
              <Button 
                onClick={() => navigate('/auth/signup')}
                variant="outline"
              >
                Try Signing Up Again
              </Button>
              <Button 
                onClick={() => navigate('/auth/signin')}
                variant="link"
              >
                Already have an account? Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-green-600">
              <CheckCircle className="h-6 w-6 mx-auto mb-2" />
              Welcome to Your Workspace!
            </CardTitle>
            <CardDescription className="text-center">
              Your account has been verified successfully
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-green-800">
                Your workspace has been created and you'll be redirected to your dashboard shortly.
              </p>
            </div>
            
            <Button 
              onClick={() => navigate('/dashboard')}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
};