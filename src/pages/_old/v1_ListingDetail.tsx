import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { 
  FileText, 
  Pencil, 
  ChevronDown,
  TrendingUp,
  Users,
  Clock,
  Building2,
  MapPin,
  DollarSign,
  Briefcase,
  Target,
  ShieldCheck,
  AlertCircle
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { formatCurrencySafe } from "@/lib/formatters";
import { useListing, useUpdateListingStatusMutation } from "@/hooks/useQueryApi";
import { LoadingOverlay } from "@/components/ui/loading-overlay";

const ListingDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // Use React Query hook to fetch listing data
  const { listing, loading, error, refetch } = useListing(id);
  
  // Mutation for updating listing status
  const updateStatusMutation = useUpdateListingStatusMutation({
    onSuccess: () => {
      refetch();
    }
  });

  // Loading state
  if (loading) {
    return <LoadingOverlay
      message="Please wait while we fetch the listing details."
      source="V1ListingDetail:Unknown:DataFetch"
    />;
  }

  // Error state
  if (error) {
    return (
      <div className="flex h-[60vh] items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Listing</h2>
          <p className="text-muted-foreground mb-4">
            There was a problem loading the listing details. Please try again.
          </p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Not found state
  if (!listing) {
    return (
      <div className="flex h-[60vh] items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold">Listing Not Found</h2>
          <p className="text-muted-foreground">The listing you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/listings')} className="mt-4">
            Back to Listings
          </Button>
        </div>
      </div>
    );
  }

  const statusColors = {
    "active": "bg-emerald-500",
    "under contract": "bg-amber-500",
    "sold": "bg-green-600",
    "confidential": "bg-purple-600",
    "archived": "bg-slate-500",
    "draft": "bg-blue-500"
  };

  const handleStatusUpdate = (status: string) => {
    updateStatusMutation.mutate({
      listingId: listing.id,
      statusUpdate: { status }
    });
  };

  // Calculate financial metrics safely
  const askingPrice = listing.askingPrice || 0;
  const cashFlow = listing.cashFlowSde || 0;
  const annualRevenue = listing.annualRevenue || 0;

  const multiple = cashFlow > 0 ? (askingPrice / cashFlow).toFixed(1) : 'N/A';
  const roi = askingPrice > 0 ? ((cashFlow / askingPrice) * 100).toFixed(1) : 'N/A';

  return (
    <div className="space-y-6 pb-6">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold rendyr-gradient-text">{listing.businessName}</h1>
            <Badge className={statusColors[listing.status?.toLowerCase() as keyof typeof statusColors] || "bg-muted"}>
              {listing.status}
            </Badge>
          </div>
          <div className="mt-1 flex items-center gap-4 text-sm text-muted-foreground">
            <span>{listing.industry}</span>
            <span>•</span>
            <span className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {listing.generalLocation}
            </span>
            {listing.yearEstablished && (
              <>
                <span>•</span>
                <span>Est. {listing.yearEstablished}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button className="rendyr-btn" variant="outline" size="sm">
            <FileText className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button 
            className="rendyr-btn"
            size="sm"
            onClick={() => navigate(`/listings/${id}/edit`)}
          >
            <Pencil className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                className="rendyr-btn" 
                size="sm"
                disabled={updateStatusMutation.isPending}
              >
                Status
                {updateStatusMutation.isPending ? (
                  <Spinner size="sm" className="ml-2" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem onClick={() => handleStatusUpdate('active')}>
                Mark Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusUpdate('under contract')}>
                Under Contract
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusUpdate('sold')}>
                Mark Sold
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusUpdate('archived')}>
                Archive
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Status update feedback */}
      {updateStatusMutation.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to update status. Please try again.
          </AlertDescription>
        </Alert>
      )}

      {/* Key Metrics Row */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="rendyr-card">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Asking Price</p>
                <h3 className="text-xl font-bold">{formatCurrencySafe(listing.askingPrice)}</h3>
              </div>
              <DollarSign className="h-4 w-4 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="rendyr-card">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Annual Revenue</p>
                <h3 className="text-xl font-bold">{formatCurrencySafe(listing.annualRevenue)}</h3>
              </div>
              <TrendingUp className="h-4 w-4 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="rendyr-card">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Cash Flow (SDE)</p>
                <h3 className="text-xl font-bold">{formatCurrencySafe(listing.cashFlowSde)}</h3>
              </div>
              <TrendingUp className="h-4 w-4 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="rendyr-card">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-muted-foreground">Employees</p>
                <h3 className="text-xl font-bold">{listing.employees || 'N/A'}</h3>
              </div>
              <Users className="h-4 w-4 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Left Column */}
        <div className="space-y-4">
          <Card className="rendyr-card">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building2 className="h-5 w-5 text-primary" />
                Business Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                  {listing.details?.businessDescription ||
                    `${listing.businessName} is a well-established ${listing.industry?.toLowerCase()} business located in ${listing.generalLocation}.`}
                </p>
              <div className="grid gap-3 sm:grid-cols-2">
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Location</p>
                  <p className="text-sm">{listing.generalLocation}</p>
                </div>
                {listing.ownerHoursWeek && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">Owner Hours</p>
                    <p className="text-sm">{listing.ownerHoursWeek} hrs/week</p>
                  </div>
                )}
                {listing.daysListed && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">Days Listed</p>
                    <p className="text-sm">{listing.daysListed} days</p>
                  </div>
                )}
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Team Size</p>
                  <p className="text-sm">{listing.employees || 'N/A'} employees</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="rendyr-card">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <TrendingUp className="h-5 w-5 text-primary" />
                Financial Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid gap-3 sm:grid-cols-2">
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Annual Revenue</p>
                  <p className="text-sm font-semibold">{formatCurrencySafe(listing.annualRevenue)}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Cash Flow (SDE)</p>
                  <p className="text-sm font-semibold">{formatCurrencySafe(listing.cashFlowSde)}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Multiple</p>
                  <p className="text-sm">{multiple}x</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">ROI Estimate</p>
                  <p className="text-sm">{roi}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <Card className="rendyr-card">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Target className="h-5 w-5 text-primary" />
                Growth Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                  Multiple opportunities for expansion including new markets, additional services, and operational improvements for this {listing.industry?.toLowerCase()} business.
                </p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Market expansion opportunities</li>
                <li>• Service line extensions</li>
                <li>• Operational efficiency improvements</li>
                <li>• Technology integration</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="rendyr-card">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Briefcase className="h-5 w-5 text-primary" />
                Operations & Support
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Training Period</p>
                  <p className="text-sm">30-60 days</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Support Type</p>
                  <p className="text-sm">Full operational</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Real Estate</p>
                  <p className="text-sm">Leased facility</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Financing</p>
                  <p className="text-sm">{listing.details?.financingAvailable ? 'Available' : 'Contact for details'}</p>
                </div>
              </div>
              <Separator />
              <div>
                <p className="text-xs font-medium text-muted-foreground mb-1">Reason for Sale</p>
                <p className="text-sm">
                  Owner retirement after successful operation. Comprehensive transition plan included.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="rendyr-card">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <ShieldCheck className="h-5 w-5 text-primary" />
                Additional Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid gap-3 sm:grid-cols-2">
                {listing.yearEstablished && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground">Established</p>
                    <p className="text-sm">{listing.yearEstablished}</p>
                  </div>
                )}
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Industry</p>
                  <p className="text-sm">{listing.industry}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Status</p>
                  <p className="text-sm">{listing.status}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Team Visibility</p>
                  <p className="text-sm">All</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Panel */}
      <Card className="rendyr-card">
        <CardContent className="flex items-center justify-between py-3">
          <div className="flex items-center gap-4">
            <Button className="rendyr-btn" variant="outline" size="sm">
              <FileText className="mr-2 h-4 w-4" />
              Generate PDF
            </Button>
            <div className="flex items-center gap-2">
              <input 
                type="checkbox" 
                id="include-portfolio" 
                className="h-4 w-4 rounded border-primary" 
              />
              <label className="text-sm" htmlFor="include-portfolio">
                Include in Portfolio
              </label>
            </div>
            <div className="flex items-center gap-2">
              <input 
                type="checkbox" 
                id="mark-confidential" 
                className="h-4 w-4 rounded border-primary" 
                checked={listing.status?.toLowerCase() === 'confidential'}
                onChange={(e) => {
                  if (e.target.checked) {
                    handleStatusUpdate('confidential');
                  }
                }}
              />
              <label className="text-sm" htmlFor="mark-confidential">
                Confidential
              </label>
            </div>
          </div>
          <Button variant="outline" className="rendyr-btn" size="sm">
            Share Listing
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ListingDetail;