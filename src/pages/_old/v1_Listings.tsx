import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  ChevronLeft, 
  ChevronRight, 
  Edit, 
  X,
  FilterX,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Upload
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { formatCurrencySafe, formatCurrencyAbbreviated } from "@/lib/formatters";
import { useListings } from "@/hooks/useQueryApi";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import CsvImportModal2 from "@/components/forms/CsvImportModal2";

// Filter interface
interface ListingFilters {
  status: string;
  industry: string;
  minPrice: string;
  maxPrice: string;
  location: string;
  assignedTo: string;
  sortBy: string;
  sortOrder: string;
}

// Sort options with display names (using snake_case values that match the API)
const sortOptions = [
  { value: "created_at:desc", label: "Date Created (Newest)", icon: ArrowDown },
  { value: "created_at:asc", label: "Date Created (Oldest)", icon: ArrowUp },
  { value: "updated_at:desc", label: "Last Updated (Newest)", icon: ArrowDown },
  { value: "updated_at:asc", label: "Last Updated (Oldest)", icon: ArrowUp },
  { value: "asking_price:desc", label: "Price (High to Low)", icon: ArrowDown },
  { value: "asking_price:asc", label: "Price (Low to High)", icon: ArrowUp },
  { value: "business_name:asc", label: "Business Name (A-Z)", icon: ArrowUp },
  { value: "business_name:desc", label: "Business Name (Z-A)", icon: ArrowDown },
  { value: "date_listed:desc", label: "Date Listed (Newest)", icon: ArrowDown },
  { value: "date_listed:asc", label: "Date Listed (Oldest)", icon: ArrowUp },
  { value: "days_listed:desc", label: "Days Listed (Most)", icon: ArrowDown },
  { value: "days_listed:asc", label: "Days Listed (Least)", icon: ArrowUp },
];

const Listings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState<ListingFilters>({
    status: "all",
    industry: "all",
    minPrice: "",
    maxPrice: "",
    location: "",
    assignedTo: "all",
    sortBy: "created_at",
    sortOrder: "desc"
  });
  const itemsPerPage = 25;
  const navigate = useNavigate();

  // Get current sort value for display
  const currentSortValue = `${filters.sortBy}:${filters.sortOrder}`;
  const currentSortOption = sortOptions.find(option => option.value === currentSortValue) || sortOptions[0];

  // Build query parameters from filters
  const queryParams = React.useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    };

    if (filters.status && filters.status !== "all") params.status = filters.status;
    if (filters.industry && filters.industry !== "all") params.industry = filters.industry;
    if (filters.location) params.location = filters.location;
    if (filters.assignedTo && filters.assignedTo !== "all") params.assignedTo = filters.assignedTo;
    
    // Handle price range
    if (filters.minPrice) {
      const minPrice = parseFloat(filters.minPrice.replace(/[,$]/g, ''));
      if (!isNaN(minPrice)) params.minPrice = minPrice;
    }
    if (filters.maxPrice) {
      const maxPrice = parseFloat(filters.maxPrice.replace(/[,$]/g, ''));
      if (!isNaN(maxPrice)) params.maxPrice = maxPrice;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, filters]);

  // Use React Query hook for listings
  const { 
    listings, 
    pagination, 
    loading, 
    error, 
    isRefetching, 
    refetch 
  } = useListings(queryParams);

  // Calculate total pages from API pagination
  const totalPages = pagination.pages || 1;

  // Reset to first page when search or filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters]);

  // Helper function to update filters
  const updateFilter = (key: keyof ListingFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split(':');
    setFilters(prev => ({ ...prev, sortBy, sortOrder }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: "all",
      industry: "all",
      minPrice: "",
      maxPrice: "",
      location: "",
      assignedTo: "all",
      sortBy: "created_at",
      sortOrder: "desc"
    });
  };

  // Check if any filters are active
  const hasActiveFilters = Object.entries(filters).some(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== "" && value !== "all"
  );

  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== "" && value !== "all"
  ).length;

  // Enhanced glassmorphic status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    switch (status.toLowerCase()) {
      case "active":
        return (
          <Badge className="bg-emerald-500/90 text-white border-emerald-400/50 backdrop-blur-sm shadow-sm">
            Active
          </Badge>
        );
      case "under contract":
        return (
          <Badge className="bg-amber-500/90 text-white border-amber-400/50 backdrop-blur-sm shadow-sm">
            Under Contract
          </Badge>
        );
      case "sold":
        return (
          <Badge className="bg-green-600/90 text-white border-green-500/50 backdrop-blur-sm shadow-sm">
            Sold
          </Badge>
        );
      case "confidential":
        return (
          <Badge className="bg-purple-600/90 text-white border-purple-500/50 backdrop-blur-sm shadow-sm">
            Confidential
          </Badge>
        );
      case "archived":
        return (
          <Badge className="bg-slate-500/90 text-white border-slate-400/50 backdrop-blur-sm shadow-sm">
            Archived
          </Badge>
        );
      case "draft":
        return (
          <Badge className="bg-blue-500/90 text-white border-blue-400/50 backdrop-blur-sm shadow-sm">
            Draft
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-orange-500/90 text-white border-orange-400/50 backdrop-blur-sm shadow-sm">
            Pending
          </Badge>
        );
      case "withdrawn":
        return (
          <Badge className="bg-red-500/90 text-white border-red-400/50 backdrop-blur-sm shadow-sm">
            Withdrawn
          </Badge>
        );
      default:
        return (
          <Badge className="bg-muted/90 text-muted-foreground border-border/50 backdrop-blur-sm shadow-sm">
            {status}
          </Badge>
        );
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const handleRowClick = (listingId: string) => {
    navigate(`/listings/${listingId}`);
  };

  const handleEditClick = (e: React.MouseEvent, listingId: string) => {
    e.stopPropagation(); // Prevent row click when clicking edit button
    navigate(`/listings/${listingId}/edit`);
  };

  return (
    <div className="h-[80vh] flex flex-col texture-bg overflow-hidden">
      {/* Header Section - Fixed Height */}
      <div className="flex-shrink-0 p-4 pb-2">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between fade-in-up pb-10">
          <div>
            <h1 className="text-2xl font-bold tracking-tight rendyr-gradient-text">Listings</h1>
            <p className="text-sm text-muted-foreground">
              Manage your business listings
            </p>
          </div>
          <div className="mt-3 flex gap-2 sm:mt-0">
            <CsvImportModal2 onSuccess={() => refetch()}>
              <Button variant="outline" size="sm" className="rendyr-btn">
                <Upload className="mr-2 h-4 w-4" />
                Import CSV
              </Button>
            </CsvImportModal2>
            
            <Button className="rendyr-btn" size="sm" asChild>
              <Link to="/listings/new">
                <Plus className="h-4 w-4" />
                New Listing
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area - Flexible Height */}
      <div className="flex-1 flex flex-col px-4 pb-4 min-h-0">
        <Card className="rendyr-card fade-in-up stagger-1 flex-1 flex flex-col min-h-0">
          {/* Card Header - Fixed Height */}
          <CardHeader className="pb-2 flex-shrink-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <CardTitle className="text-lg">All Listings</CardTitle>
              <div className="text-xs text-muted-foreground">
                {loading ? "Loading..." : `Showing ${((currentPage - 1) * itemsPerPage) + 1}-${Math.min(currentPage * itemsPerPage, pagination.total)} of ${pagination.total} listings`}
              </div>
            </div>
          </CardHeader>
          
          {/* Card Content - Flexible Height */}
          <CardContent className="flex-1 flex flex-col min-h-0 pt-0">
            {/* Search and Controls - Fixed Height */}
            <div className="mb-3 flex flex-col gap-2 sm:flex-row fade-in-up stagger-2 flex-shrink-0">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search listings..."
                  className="pl-8 h-9 bg-background/50 backdrop-blur-sm border-border/50 focus:bg-background/80 transition-all duration-300"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                {/* Unified Sort Dropdown */}
                <Select value={currentSortValue} onValueChange={handleSortChange}>
                  <SelectTrigger className="h-9 bg-background/50 backdrop-blur-sm border-border/50 w-48 text-xs">
                    <div className="flex items-center gap-2">
                      <ArrowUpDown className="h-3 w-3" />
                      <span className="truncate">{currentSortOption.label}</span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => {
                      const IconComponent = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-3 w-3" />
                            {option.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                
                {/* Filter Modal Trigger */}
                <Popover open={filterModalOpen} onOpenChange={setFilterModalOpen}>
                  <PopoverTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className={`gap-2 rendyr-btn bg-background/50 backdrop-blur-sm border-border/50 hover:bg-accent hover:text-accent-foreground ${
                        hasActiveFilters ? 'bg-accent text-accent-foreground border-accent' : ''
                      }`}
                    >
                      <Filter className="h-4 w-4" />
                      <span>Filter</span>
                      {hasActiveFilters && (
                        <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs flex items-center justify-center">
                          {activeFilterCount}
                        </Badge>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent 
                    className="w-80 p-4 bg-background/95 backdrop-blur-md border-border/50 shadow-xl" 
                    align="end"
                    sideOffset={4}
                  >
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-sm">Filter Listings</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setFilterModalOpen(false)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {/* Status Filter */}
                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">Status</Label>
                        <Select 
                          value={filters.status} 
                          onValueChange={(value) => updateFilter('status', value)}
                        >
                          <SelectTrigger className="h-8 text-xs bg-background/80">
                            <SelectValue placeholder="All Statuses" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="under contract">Under Contract</SelectItem>
                            <SelectItem value="sold">Sold</SelectItem>
                            <SelectItem value="confidential">Confidential</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                            <SelectItem value="draft">Draft</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Industry Filter */}
                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">Industry</Label>
                        <Select 
                          value={filters.industry} 
                          onValueChange={(value) => updateFilter('industry', value)}
                        >
                          <SelectTrigger className="h-8 text-xs bg-background/80">
                            <SelectValue placeholder="All Industries" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Industries</SelectItem>
                            <SelectItem value="Restaurant">Restaurant</SelectItem>
                            <SelectItem value="Technology">Technology</SelectItem>
                            <SelectItem value="Healthcare">Healthcare</SelectItem>
                            <SelectItem value="Retail">Retail</SelectItem>
                            <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                            <SelectItem value="Professional Services">Professional Services</SelectItem>
                            <SelectItem value="Home Services">Home Services</SelectItem>
                            <SelectItem value="Education">Education</SelectItem>
                            <SelectItem value="Automotive">Automotive</SelectItem>
                            <SelectItem value="Food & Beverage">Food & Beverage</SelectItem>
                            <SelectItem value="Health & Fitness">Health & Fitness</SelectItem>
                            <SelectItem value="Construction">Construction</SelectItem>
                            <SelectItem value="Marketing">Marketing</SelectItem>
                            <SelectItem value="E-commerce">E-commerce</SelectItem>
                            <SelectItem value="Hospitality">Hospitality</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Price Range */}
                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">Price Range</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            placeholder="Min price"
                            value={filters.minPrice}
                            onChange={(e) => updateFilter('minPrice', e.target.value)}
                            className="h-8 text-xs bg-background/80"
                          />
                          <Input
                            placeholder="Max price"
                            value={filters.maxPrice}
                            onChange={(e) => updateFilter('maxPrice', e.target.value)}
                            className="h-8 text-xs bg-background/80"
                          />
                        </div>
                      </div>

                      {/* Location Filter */}
                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">Location</Label>
                        <Input
                          placeholder="City, State"
                          value={filters.location}
                          onChange={(e) => updateFilter('location', e.target.value)}
                          className="h-8 text-xs bg-background/80"
                        />
                      </div>

                      {/* Assigned To Filter */}
                      <div className="space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground">Assigned To</Label>
                        <Select 
                          value={filters.assignedTo} 
                          onValueChange={(value) => updateFilter('assignedTo', value)}
                        >
                          <SelectTrigger className="h-8 text-xs bg-background/80">
                            <SelectValue placeholder="All Team Members" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Team Members</SelectItem>
                            <SelectItem value="me">Assigned to Me</SelectItem>
                            <SelectItem value="unassigned">Unassigned</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2 border-t border-border/50">
                        <Button 
                          variant="outline" 
                          onClick={clearFilters}
                          disabled={!hasActiveFilters}
                          size="sm"
                          className="flex-1 h-8 text-xs"
                        >
                          <FilterX className="h-3 w-3 mr-1" />
                          Clear
                        </Button>
                        <Button 
                          onClick={() => setFilterModalOpen(false)}
                          size="sm"
                          className="flex-1 h-8 text-xs rendyr-btn"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            {/* Table Container - Scrollable Area */}
            <div className="flex-1 min-h-0 fade-in-up stagger-3">
              {error && (
                <Alert className="mb-4">
                  <AlertDescription>
                    Failed to load listings. <Button variant="link" className="p-0 h-auto" onClick={() => refetch()}>Try again</Button>
                  </AlertDescription>
                </Alert>
              )}
              <div className="h-full rounded-lg border border-border/50 bg-background/30 backdrop-blur-sm overflow-hidden flex flex-col">
                {loading ? (
                  <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
                    <LoadingOverlay
                      message="Loading listings..."
                      overlay={false}
                    />
                  </div>
                ) : listings.length === 0 ? (
                  <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
                    <div className="rounded-full bg-muted/20 p-4 mb-3 backdrop-blur-sm">
                      <FileText className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <p className="text-muted-foreground font-medium mb-1">
                      No listings found
                    </p>
                    <p className="text-muted-foreground/80 text-sm">
                      Try adjusting your search criteria or create a new listing
                    </p>
                  </div>
                ) : (
                  <>
                    {/* Table Header - Fixed */}
                    <div className="border-b border-border/50 bg-muted/30 flex-shrink-0">
                      <Table>
                        <TableHeader>
                          <TableRow className="border-b-0 h-10">
                            <TableHead className="font-semibold text-xs">Business Name</TableHead>
                            <TableHead className="font-semibold text-xs">Industry</TableHead>
                            <TableHead className="font-semibold text-xs">Asking Price</TableHead>
                            <TableHead className="font-semibold text-xs">Location</TableHead>
                            <TableHead className="font-semibold text-xs">Status</TableHead>
                            <TableHead className="w-12"></TableHead>
                          </TableRow>
                        </TableHeader>
                      </Table>
                    </div>
                    
                    {/* Table Body - Scrollable */}
                    <div className="flex-1 overflow-auto">
                      <Table>
                        <TableBody>
                          {listings.map((listing, index) => (
                            <TableRow 
                              key={listing.id}
                              className="border-b border-border/30 hover:bg-muted/20 transition-all duration-300 cursor-pointer h-12"
                              onClick={() => handleRowClick(listing.id)}
                            >
                              <TableCell className="font-medium py-2">
                                <span className="text-primary font-semibold hover:text-primary/80 transition-colors text-sm">
                                  {listing.businessName}
                                </span>
                              </TableCell>
                              <TableCell className="text-muted-foreground font-medium text-sm py-2">{listing.industry}</TableCell>
                              <TableCell className="font-semibold text-sm py-2">{formatCurrencyAbbreviated(listing.askingPrice)}</TableCell>
                              <TableCell className="text-muted-foreground text-sm py-2">{listing.generalLocation}</TableCell>
                              <TableCell className="py-2">
                                <StatusBadge status={listing.status} />
                              </TableCell>
                              <TableCell className="w-12 py-2">
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-7 w-7 p-0 hover:bg-muted/50" 
                                  onClick={(e) => handleEditClick(e, listing.id)}
                                  title="Edit listing"
                                >
                                  <Edit className="h-3.5 w-3.5" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Pagination Controls - Fixed Height */}
            {!loading && listings.length > 0 && totalPages > 1 && (
              <div className="flex items-center justify-between mt-3 fade-in-up stagger-4 flex-shrink-0">
                <div className="text-xs text-muted-foreground flex items-center gap-2">
                  Page {currentPage} of {totalPages}
                  {isRefetching && <Spinner size="xs" />}
                </div>
                
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="rendyr-btn bg-background/50 backdrop-blur-sm border-border/50 hover:bg-accent hover:text-accent-foreground h-8 px-2"
                  >
                    <ChevronLeft className="h-3 w-3" />
                    <span className="hidden sm:inline ml-1">Previous</span>
                  </Button>
                  
                  <div className="flex items-center gap-1">
                    {generatePageNumbers().map((page) => (
                      <Button
                        key={page}
                        variant={page === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                        className={`w-8 h-8 p-0 text-xs ${
                          page === currentPage 
                            ? "rendyr-btn" 
                            : "rendyr-btn bg-background/50 backdrop-blur-sm border-border/50 hover:bg-accent hover:text-accent-foreground"
                        }`}
                      >
                        {page}
                      </Button>
                    ))}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="rendyr-btn bg-background/50 backdrop-blur-sm border-border/50 hover:bg-accent hover:text-accent-foreground h-8 px-2"
                  >
                    <span className="hidden sm:inline mr-1">Next</span>
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Listings;