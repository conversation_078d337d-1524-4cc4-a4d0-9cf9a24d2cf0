import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown,
  Plus, 
  Building2, 
  DollarSign, 
  Users, 
  Calendar,
  Target,
  Award,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useListings } from "@/hooks/useQueryApi";

const Dashboard: React.FC = () => {
  const { listings, loading, error } = useListings({ 
    limit: 8, 
    sortBy: 'created_at', 
    sortOrder: 'asc' 
  });

  const kpiData = React.useMemo(() => {
    if (loading || !listings.length) {
      return {
        activeListings: 0,
        underContract: 0,
        closedThisMonth: 0,
        pipelineCommissions: 0,
        totalRevenue: 0,
        avgDeal: 0,
      };
    }

    const activeListings = listings.filter(l => l.status === 'Active').length;
    const underContract = listings.filter(l => l.status === 'Under Contract').length;
    const closedThisMonth = listings.filter(l => l.status === 'Sold').length;
    
    const totalRevenue = listings
      .filter(l => l.status === 'Sold')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0), 0);
      
    const pipelineCommissions = listings
      .filter(l => l.status === 'Active' || l.status === 'Under Contract')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0) * 0.03, 0);

    const avgDeal = closedThisMonth > 0 ? totalRevenue / closedThisMonth : 0;

    return {
      activeListings,
      underContract,
      closedThisMonth,
      pipelineCommissions,
      totalRevenue,
      avgDeal,
    };
  }, [listings, loading]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'text-emerald-600 bg-emerald-50';
      case 'Under Contract': return 'text-amber-600 bg-amber-50';
      case 'Sold': return 'text-blue-600 bg-blue-50';
      default: return 'text-slate-600 bg-slate-50';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
      {/* Header Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-primary/5 via-primary/10 to-accent/5 border-b border-border/50">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="relative p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-xl bg-primary/10">
                  <Activity className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-primary/80 to-accent bg-clip-text text-transparent">
                    Welcome Back
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Here's what's happening with your business portfolio
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <Button size="lg" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg" asChild>
                <Link to="/listings/new">
                  <Plus className="mr-2 h-5 w-5" />
                  Create Listing
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link to="/reports">
                  <Target className="mr-2 h-5 w-5" />
                  View Reports
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-8 space-y-8">
        {/* KPI Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {/* Active Listings */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Active Listings</p>
                  <p className="text-3xl font-bold text-primary">{kpiData.activeListings}</p>
                </div>
                <div className="p-3 rounded-full bg-primary/10">
                  <Building2 className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2 text-sm">
                <TrendingUp className="h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600">+12% from last month</span>
              </div>
            </CardContent>
          </Card>

          {/* Under Contract */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Under Contract</p>
                  <p className="text-3xl font-bold text-amber-600">{kpiData.underContract}</p>
                </div>
                <div className="p-3 rounded-full bg-amber-100 dark:bg-amber-900/20">
                  <Clock className="h-6 w-6 text-amber-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2 text-sm">
                <ArrowUpRight className="h-4 w-4 text-amber-500" />
                <span className="text-amber-600">Processing deals</span>
              </div>
            </CardContent>
          </Card>

          {/* Closed This Month */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Closed This Month</p>
                  <p className="text-3xl font-bold text-emerald-600">{kpiData.closedThisMonth}</p>
                </div>
                <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/20">
                  <CheckCircle className="h-6 w-6 text-emerald-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2 text-sm">
                <Award className="h-4 w-4 text-emerald-500" />
                <span className="text-emerald-600">Great progress!</span>
              </div>
            </CardContent>
          </Card>

          {/* Pipeline Commissions */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Pipeline Value</p>
                  <p className="text-3xl font-bold text-blue-600">{formatCurrency(kpiData.pipelineCommissions)}</p>
                </div>
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4">
                <Progress value={75} className="h-2" />
                <p className="text-xs text-muted-foreground mt-2">75% to monthly goal</p>
              </div>
            </CardContent>
          </Card>

          {/* Total Revenue */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-3xl font-bold text-purple-600">{formatCurrency(kpiData.totalRevenue)}</p>
                </div>
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/20">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2 text-sm">
                <ArrowUpRight className="h-4 w-4 text-purple-500" />
                <span className="text-purple-600">+25% vs last month</span>
              </div>
            </CardContent>
          </Card>

          {/* Average Deal Size */}
          <Card className="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Avg Deal Size</p>
                  <p className="text-3xl font-bold text-indigo-600">{formatCurrency(kpiData.avgDeal)}</p>
                </div>
                <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/20">
                  <Target className="h-6 w-6 text-indigo-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-indigo-500" />
                <span className="text-indigo-600">This month</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity & Quick Actions */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Recent Listings */}
          <Card className="xl:col-span-2 border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardHeader className="border-b border-border/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <Eye className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Recent Listings</CardTitle>
                    <CardDescription>Latest additions to your portfolio</CardDescription>
                  </div>
                </div>
                <Button variant="ghost" asChild>
                  <Link to="/listings">View All →</Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {listings.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-12 text-center">
                  <div className="p-3 rounded-full bg-muted/30">
                    <Building2 className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold">No listings yet</h3>
                  <p className="mt-1 text-sm text-muted-foreground">Create your first listing to see it here.</p>
                  <Button className="mt-4" asChild>
                    <Link to="/listings/new">
                      <Plus className="mr-2 h-4 w-4" />
                      Create Listing
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-0">
                  {listings.slice(0, 6).map((listing, index) => (
                    <div key={listing.id} className="flex items-center justify-between p-6 border-b border-border/30 last:border-b-0 hover:bg-muted/30 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="p-2 rounded-lg bg-slate-100 dark:bg-slate-800">
                          <Building2 className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <div className="space-y-1">
                          <h4 className="font-semibold text-sm">{listing.businessName || `Business ${index + 1}`}</h4>
                          <p className="text-xs text-muted-foreground">{listing.industry || 'Various Industry'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-semibold text-sm">{formatCurrency(listing.askingPrice || 0)}</p>
                          <p className="text-xs text-muted-foreground">Asking Price</p>
                        </div>
                        <Badge className={`text-xs font-medium ${getStatusColor(listing.status)}`}>
                          {listing.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-accent/10">
                  <Users className="h-5 w-5 text-accent" />
                </div>
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <Link to="/listings/new">
                    <Plus className="mr-3 h-4 w-4" />
                    Add New Listing
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <Link to="/team">
                    <Users className="mr-3 h-4 w-4" />
                    Manage Team
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <Link to="/reports">
                    <Activity className="mr-3 h-4 w-4" />
                    View Analytics
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="ghost" asChild>
                  <Link to="/settings">
                    <AlertCircle className="mr-3 h-4 w-4" />
                    Workspace Settings
                  </Link>
                </Button>
              </div>

              {/* Progress Section */}
              <div className="mt-8 p-4 rounded-lg bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20">
                <div className="flex items-center gap-3 mb-3">
                  <Award className="h-5 w-5 text-primary" />
                  <h3 className="font-semibold">Monthly Progress</h3>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Listings Goal</span>
                      <span>{kpiData.activeListings}/20</span>
                    </div>
                    <Progress value={(kpiData.activeListings / 20) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Revenue Target</span>
                      <span>{Math.round((kpiData.totalRevenue / 50000) * 100)}%</span>
                    </div>
                    <Progress value={(kpiData.totalRevenue / 50000) * 100} className="h-2" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;