import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, Mail, LogOut } from 'lucide-react';

export const WorkspaceSuspended: React.FC = () => {
  const { signOut, workspace } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = () => {
    // Navigate immediately for instant user feedback
    navigate('/', { replace: true });

    // Handle sign-out cleanup in background
    signOut().catch((error) => {
      console.error('Background sign out error:', error);
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-red-900">Workspace Suspended</CardTitle>
          <CardDescription>
            Your workspace access has been temporarily suspended
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              The workspace "{workspace?.company_name}" has been suspended. 
              This may be due to billing issues, policy violations, or administrative action.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              To resolve this issue, please:
            </p>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Check your billing status and update payment information</li>
              <li>• Review our terms of service and usage policies</li>
              <li>• Contact our support team for assistance</li>
            </ul>
          </div>

          <div className="flex flex-col gap-2">
            <Button className="w-full">
              <Mail className="mr-2 h-4 w-4" />
              Contact Support
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={handleSignOut}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};