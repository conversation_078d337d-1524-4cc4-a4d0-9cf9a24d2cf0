import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Home, ChevronRight, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ListingForm2 from '@/components/forms/ListingForm2';
import { useListing } from '@/hooks/useQueryApi';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

export default function EditListing() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // Use React Query hook to fetch listing data
  const { listing, loading, error, refetch } = useListing(id);

  // Loading state
  if (loading) {
    return <LoadingOverlay message="Please wait while we fetch the listing details." />;
  }

  // Error state
  if (error || !listing) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-6xl">
        <div className="flex h-[60vh] items-center justify-center">
          <div className="text-center max-w-md">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">
              {error ? "Error Loading Listing" : "Listing Not Found"}
            </h2>
            <p className="text-muted-foreground mb-4">
              {error 
                ? "There was a problem loading the listing details. Please try again."
                : "The listing you're trying to edit doesn't exist."
              }
            </p>
            <div className="flex gap-2 justify-center">
              {error && (
                <Button onClick={() => refetch()} variant="outline">
                  Try Again
                </Button>
              )}
              <Button onClick={() => navigate('/listings')} variant="default">
                Back to Listings
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Convert API data to form format
  const convertToFormData = (listing: any) => {
    // Helper function to safely format currency
    const formatCurrencyForForm = (value: any) => {
      if (!value) return '';
      if (typeof value === 'string') {
        if (value.startsWith('$')) return value;
        const num = parseFloat(value.replace(/[,$]/g, ''));
        return isNaN(num) ? '' : `${num.toLocaleString()}`;
      }
      if (typeof value === 'number') {
        return `${value.toLocaleString()}`;
      }
      return '';
    };

    // Extract details object for easier access
    const details = listing.details || {};

    return {
      // Basic listing information
      businessName: listing.businessName || '',
      industry: listing.industry || '',
      askingPrice: formatCurrencyForForm(listing.askingPrice),
      cashFlow: formatCurrencyForForm(listing.cashFlowSde),
      status: listing.status || 'active',
      annualRevenue: formatCurrencyForForm(listing.annualRevenue),
      location: listing.generalLocation || '',
      yearEstablished: listing.yearEstablished?.toString() || '',
      employees: listing.employees?.toString() || '',
      ownerHours: listing.ownerHoursWeek?.toString() || '',
      
      // Business descriptions from details
      businessDescription: details.businessDescription || '',
      briefDescription: details.briefDescription || '',
      
      // Financial information
      revenue2023: formatCurrencyForForm(listing.annualRevenue),
      ebitda2023: formatCurrencyForForm(listing.cashFlowSde),
      
      // Additional details from the details object
      financialDetails: details.financialDetails || '',
      operations: details.operations || '',
      growthOpportunities: details.growthOpportunities || '',
      reasonForSale: details.reasonForSale || '',
      trainingPeriod: details.trainingPeriod || '',
      supportType: details.supportType || '',
      financingAvailable: details.financingAvailable || false,
      equipmentHighlights: details.equipmentHighlights || '',
      supplierRelationships: details.supplierRelationships || '',
      realEstateStatus: details.realEstateStatus || '',
      leaseDetails: details.leaseDetails || '',
      
      // Fields that may not be in the API response yet but are in the form
      assetsIncluded: '',
      inventoryValue: '',
      businessModel: '',
      keyFeatures: '',
      competitiveAdvantages: '',
      customerBase: '',
      keyEmployeeInfo: '',
      specialNotes: '',
      
      // Property-related fields (for mixed-use listings)
      title: listing.title || '',
      description: listing.description || '',
      price: formatCurrencyForForm(listing.price),
      address: listing.address || '',
      city: listing.city || '',
      state: listing.state || '',
      zipCode: listing.zipCode || '',
      propertyType: listing.propertyType || '',
      squareFootage: listing.squareFootage?.toString() || '',
      lotSize: listing.lotSize || '',
      yearBuilt: listing.yearBuilt?.toString() || '',
      bedrooms: listing.bedrooms?.toString() || '',
      bathrooms: listing.bathrooms?.toString() || '',
      listingType: listing.listingType || 'business_sale',
      teamVisibility: listing.teamVisibility || 'all',
      
      // Media and additional info
      virtualTourUrl: listing.virtualTourUrl || '',
      mlsNumber: listing.mlsNumber || '',
      listingDate: listing.listingDate || '',
      expirationDate: listing.expirationDate || ''
    };
  };

  const initialData = convertToFormData(listing);

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center text-sm text-muted-foreground mb-4">
          <Home className="h-4 w-4 mr-2" />
          <span>Dashboard</span>
          <ChevronRight className="h-4 w-4 mx-2" />
          <span>Listings</span>
          <ChevronRight className="h-4 w-4 mx-2" />
          <span>Edit Listing</span>
        </div>
        
        <h1 className="text-4xl font-bold mb-2">Edit Listing</h1>
        <p className="text-muted-foreground">
          Make changes to <span className="font-medium">{listing.businessName}</span>
        </p>
      </div>

      <ListingForm2
        initialData={initialData}
        isEditing={true}
        listingId={id}
      />
    </div>
  );
}