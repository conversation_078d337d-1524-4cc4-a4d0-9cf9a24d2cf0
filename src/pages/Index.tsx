import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { SignInForm } from "@/components/auth/SignInForm";
import { SignUpForm } from "@/components/auth/SignUpForm";
import { Button } from "@/components/ui/button";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import { Building2, Users, BarChart3 } from "lucide-react";

const Index = () => {
  const { user, loading, signingOut } = useAuth();
  const [showSignUp, setShowSignUp] = useState(false);
  const navigate = useNavigate();
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Redirect authenticated users to dashboard with a small delay to handle sign-out race condition
  useEffect(() => {
    // Clear any existing timeout
    if (redirectTimeoutRef.current) {
      clearTimeout(redirectTimeoutRef.current);
      redirectTimeoutRef.current = null;
    }

    // Only redirect if we have a user and we're not loading and not signing out
    if (user && !loading && !signingOut) {
      // Add a small delay to allow sign-out process to complete if in progress
      redirectTimeoutRef.current = setTimeout(() => {
        // Double-check user is still authenticated and not signing out before redirecting
        if (user && !signingOut) {
          navigate("/dashboard", { replace: true });
        }
      }, 100);
    }

    // Cleanup timeout on unmount
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, [user, loading, signingOut, navigate]);

  // Only show login page if user is not authenticated and not loading
  if (!user && !loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row items-center justify-between min-h-screen">
            {/* Left side - Branding and features */}
            <div className="flex-1 lg:pr-12 mb-8 lg:mb-0">
              <div className="max-w-lg">
                <div className="flex items-center mb-6">
                  <img
                    src="/lovable-uploads/8e99d9d7-748b-4512-8bd7-54ca83e3b8dd.png"
                    alt="Rendyr Logo"
                    className="h-12"
                  />
                </div>

                <h1 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                  Business Brokerage Made Simple
                </h1>

                <p className="text-xl text-muted-foreground mb-8">
                  Streamline your business brokerage operations with powerful
                  listing management, team collaboration, and performance
                  analytics.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Building2 className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Listing Management</h3>
                      <p className="text-sm text-muted-foreground">
                        Organize and track all your business listings
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Team Collaboration</h3>
                      <p className="text-sm text-muted-foreground">
                        Work together with your team efficiently
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <BarChart3 className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Performance Analytics</h3>
                      <p className="text-sm text-muted-foreground">
                        Track your success with detailed insights
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Authentication forms */}
            <div className="flex-1 lg:max-w-md w-full">
              <div className="bg-card/80 backdrop-blur-sm border rounded-lg p-8 shadow-lg">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold mb-2">
                    {showSignUp ? "Create Your Workspace" : "Welcome Back"}
                  </h2>
                  <p className="text-muted-foreground">
                    {showSignUp
                      ? "Get started with your business brokerage workspace"
                      : "Sign in to access your workspace"}
                  </p>
                </div>

                {showSignUp ? (
                  <SignUpForm onSwitchToSignIn={() => setShowSignUp(false)} />
                ) : (
                  <div className="space-y-4">
                    <SignInForm />
                    <div className="text-center">
                      <Button
                        variant="link"
                        onClick={() => setShowSignUp(true)}
                        className="text-sm"
                      >
                        Don't have an account? Create workspace
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while determining auth status
  if (loading) {
    return <LoadingOverlay message="Loading..." />;
  }

  return null;
};

export default Index;
