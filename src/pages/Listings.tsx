import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Plus, 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  MapPin, 
  Calendar, 
  TrendingUp, 
  Building2, 
  DollarSign, 
  Users, 
  Clock, 
  Eye, 
  Edit, 
  Star, 
  Upload,
  SortAsc,
  SortDesc,
  X,
  FilterX
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { formatCurrencySafe, formatCurrencyAbbreviated } from "@/lib/formatters";
import { useListings } from "@/hooks/useQueryApi";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingOverlay, ButtonSpinner } from "@/components/ui/loading-overlay";
import CsvImportModal2 from "@/components/forms/CsvImportModal2";
import { cn } from "@/lib/utils";

// Filter interface
interface ListingFilters {
  status: string;
  industry: string;
  minPrice: string;
  maxPrice: string;
  location: string;
  assignedTo: string;
  sortBy: string;
  sortOrder: string;
}

// View modes
type ViewMode = 'grid' | 'list';

// Sort options
const sortOptions = [
  { value: "created_at:desc", label: "Newest First", icon: SortDesc },
  { value: "created_at:asc", label: "Oldest First", icon: SortAsc },
  { value: "asking_price:desc", label: "Price: High to Low", icon: SortDesc },
  { value: "asking_price:asc", label: "Price: Low to High", icon: SortAsc },
  { value: "business_name:asc", label: "Name: A-Z", icon: SortAsc },
  { value: "business_name:desc", label: "Name: Z-A", icon: SortDesc },
];

const V2_Listings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState<ListingFilters>({
    status: "all",
    industry: "all",
    minPrice: "",
    maxPrice: "",
    location: "",
    assignedTo: "all",
    sortBy: "created_at",
    sortOrder: "asc"
  });
  const itemsPerPage = 12;
  const navigate = useNavigate();

  // Get current sort value for display
  const currentSortValue = `${filters.sortBy}:${filters.sortOrder}`;
  const currentSortOption = sortOptions.find(option => option.value === currentSortValue) || sortOptions[0];

  // Build query parameters from filters
  const queryParams = React.useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    };

    if (filters.status && filters.status !== "all") params.status = filters.status;
    if (filters.industry && filters.industry !== "all") params.industry = filters.industry;
    if (filters.location) params.location = filters.location;
    if (filters.assignedTo && filters.assignedTo !== "all") params.assignedTo = filters.assignedTo;
    
    // Handle price range
    if (filters.minPrice) {
      const minPrice = parseFloat(filters.minPrice.replace(/[,$]/g, ''));
      if (!isNaN(minPrice)) params.minPrice = minPrice;
    }
    if (filters.maxPrice) {
      const maxPrice = parseFloat(filters.maxPrice.replace(/[,$]/g, ''));
      if (!isNaN(maxPrice)) params.maxPrice = maxPrice;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, filters]);

  // Use React Query hook for listings
  const { 
    listings, 
    pagination, 
    loading, 
    error, 
    isRefetching, 
    refetch 
  } = useListings(queryParams);

  // Calculate total pages from API pagination
  const totalPages = pagination.pages || 1;

  // Reset to first page when search or filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters]);

  // Helper function to update filters
  const updateFilter = (key: keyof ListingFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split(':');
    setFilters(prev => ({ ...prev, sortBy, sortOrder }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: "all",
      industry: "all",
      minPrice: "",
      maxPrice: "",
      location: "",
      assignedTo: "all",
      sortBy: "created_at",
      sortOrder: "desc"
    });
  };

  // Check if any filters are active
  const hasActiveFilters = Object.entries(filters).some(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== "" && value !== "all"
  );

  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== "" && value !== "all"
  ).length;

  // Enhanced status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const statusConfig = {
      "active": { color: "bg-emerald-500", label: "Active" },
      "under contract": { color: "bg-amber-500", label: "Under Contract" },
      "sold": { color: "bg-green-600", label: "Sold" },
      "confidential": { color: "bg-purple-600", label: "Confidential" },
      "archived": { color: "bg-slate-500", label: "Archived" },
      "draft": { color: "bg-blue-500", label: "Draft" },
      "pending": { color: "bg-orange-500", label: "Pending" },
      "withdrawn": { color: "bg-red-500", label: "Withdrawn" },
    };

    const config = statusConfig[status.toLowerCase() as keyof typeof statusConfig] || 
                  { color: "bg-muted", label: status };

    return (
      <Badge className={`${config.color}/90 text-white border-0 backdrop-blur-sm shadow-sm`}>
        {config.label}
      </Badge>
    );
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCardClick = (listingId: string) => {
    navigate(`/listings/${listingId}`);
  };

  const handleEditClick = (e: React.MouseEvent, listingId: string) => {
    e.stopPropagation();
    navigate(`/listings/${listingId}/edit`);
  };

  // Calculate days on market
  const getDaysOnMarket = (dateListedStr: string) => {
    const dateListed = new Date(dateListedStr);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - dateListed.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header Section */}
      <div className="sticky top-0 z-40 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                Business Listings
              </h1>
              <p className="text-slate-600 dark:text-slate-400">
                Discover and manage premium business opportunities
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <CsvImportModal2 onSuccess={() => refetch()}>
                <Button variant="outline" className="gap-2  dark:hover:bg-slate-800">
                  <Upload className="h-4 w-4" />
                  Import CSV
                </Button>
              </CsvImportModal2>
              
              <Button className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg" asChild>
                <Link to="/listings/new">
                  <Plus className="h-4 w-4" />
                  New Listing
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Search and Controls */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
              <Input
                type="search"
                placeholder="Search by business name, industry, or location..."
                className="pl-12 h-12 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 focus:bg-white dark:focus:bg-slate-800 transition-all duration-300 text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            
            {/* Controls */}
            <div className="flex items-center gap-3">
              {/* Sort Dropdown */}
              <Select value={currentSortValue} onValueChange={handleSortChange}>
                <SelectTrigger className="w-48 h-12 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50">
                  <div className="flex items-center gap-2">
                    <currentSortOption.icon className="h-4 w-4" />
                    <span>{currentSortOption.label}</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => {
                    const IconComponent = option.icon;
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          {option.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              
              {/* Filter Button */}
              <Popover open={filterModalOpen} onOpenChange={setFilterModalOpen}>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className={cn(
                      "gap-2 h-12 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 dark:hover:bg-slate-800",
                      hasActiveFilters && "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"
                    )}
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filter</span>
                    {hasActiveFilters && (
                      <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs flex items-center justify-center">
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent 
                  className="w-80 p-6 bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl border-slate-200/50 dark:border-slate-700/50 shadow-xl" 
                  align="end"
                  sideOffset={8}
                >
                  <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-lg">Filter Listings</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setFilterModalOpen(false)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {/* Status Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Status</Label>
                      <Select 
                        value={filters.status} 
                        onValueChange={(value) => updateFilter('status', value)}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="under contract">Under Contract</SelectItem>
                          <SelectItem value="sold">Sold</SelectItem>
                          <SelectItem value="confidential">Confidential</SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                          <SelectItem value="draft">Draft</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Industry Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Industry</Label>
                      <Select 
                        value={filters.industry} 
                        onValueChange={(value) => updateFilter('industry', value)}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="All Industries" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Industries</SelectItem>
                          <SelectItem value="Restaurant">Restaurant</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Retail">Retail</SelectItem>
                          <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                          <SelectItem value="Professional Services">Professional Services</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Price Range */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Price Range</Label>
                      <div className="grid grid-cols-2 gap-3">
                        <Input
                          type="text"
                          inputMode="decimal"
                          placeholder="Min price"
                          value={filters.minPrice}
                          onChange={(e) => updateFilter('minPrice', e.target.value)}
                          className="h-10"
                        />
                        <Input
                          type="text"
                          inputMode="decimal"
                          placeholder="Max price"
                          value={filters.maxPrice}
                          onChange={(e) => updateFilter('maxPrice', e.target.value)}
                          className="h-10"
                        />
                      </div>
                    </div>

                    {/* Location Filter */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Location</Label>
                      <Input
                        type="text"
                        placeholder="City, State"
                        value={filters.location}
                        onChange={(e) => updateFilter('location', e.target.value)}
                        className="h-10"
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                      <Button 
                        variant="outline" 
                        onClick={clearFilters}
                        disabled={!hasActiveFilters}
                        className="flex-1"
                      >
                        <FilterX className="h-4 w-4 mr-2" />
                        Clear
                      </Button>
                      <Button 
                        onClick={() => setFilterModalOpen(false)}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                      >
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              
              {/* View Mode Toggle */}
              <div className="flex items-center bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-10 px-3"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-10 px-3"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-slate-600 dark:text-slate-400">
            <div className="flex items-center gap-2">
              {loading ? (
                <span>Loading listings...</span>
              ) : (
                <span>
                  Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, pagination.total)} of {pagination.total} listings
                </span>
              )}
              {isRefetching && <ButtonSpinner className="h-3 w-3" />}
            </div>
            
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Clear all filters
              </Button>
            )}
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
            <AlertDescription className="text-red-800 dark:text-red-200">
              Failed to load listings. <Button variant="link" className="p-0 h-auto text-red-600 dark:text-red-400" onClick={() => refetch()}>Try again</Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading ? (
          <LoadingOverlay
            message="Loading your business listings..."
            overlay={false}
          />
        ) : listings.length === 0 ? (
          /* Empty State */
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <div className="rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 mb-6">
              <Building2 className="h-12 w-12 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
              No listings found
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md">
              {searchTerm || hasActiveFilters
                ? "Try adjusting your search criteria or filters to find more listings."
                : "Get started by creating your first business listing."
              }
            </p>
            {!searchTerm && !hasActiveFilters && (
              <Button className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700" asChild>
                <Link to="/listings/new">
                  <Plus className="h-4 w-4" />
                  Create Your First Listing
                </Link>
              </Button>
            )}
          </div>
        ) : (
          /* Listings Display */
          <>
            {viewMode === 'grid' ? (
              /* Grid View */
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                {listings.map((listing, index) => (
                  <Card
                    key={listing.id}
                    className="group cursor-pointer overflow-hidden bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:bg-white dark:hover:bg-slate-800 hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-blue-400/10 transition-all duration-300 hover:-translate-y-1"
                    onClick={() => handleCardClick(listing.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between mb-3">
                        <StatusBadge status={listing.status} />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => handleEditClick(e, listing.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>

                      <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                        {listing.businessName}
                      </CardTitle>

                      <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                        <Building2 className="h-4 w-4" />
                        <span>{listing.industry}</span>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {/* Price */}
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
                          <span className="font-semibold text-lg text-slate-900 dark:text-white">
                            {formatCurrencyAbbreviated(listing.askingPrice)}
                          </span>
                        </div>

                        {/* Location */}
                        <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                          <MapPin className="h-4 w-4" />
                          <span className="truncate">{listing.generalLocation}</span>
                        </div>

                        {/* Revenue */}
                        {listing.annualRevenue && (
                          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                            <TrendingUp className="h-4 w-4" />
                            <span>Revenue: {formatCurrencyAbbreviated(listing.annualRevenue)}</span>
                          </div>
                        )}

                        {/* Employees */}
                        {listing.employees && (
                          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                            <Users className="h-4 w-4" />
                            <span>{listing.employees} employees</span>
                          </div>
                        )}

                        {/* Days on Market */}
                        <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                          <Clock className="h-4 w-4" />
                          <span>{getDaysOnMarket(listing.dateListed)} days on market</span>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 pt-2 border-t border-slate-200/50 dark:border-slate-700/50">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCardClick(listing.id);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 gap-2 hover:bg-slate-50 dark:hover:bg-slate-800"
                            onClick={(e) => handleEditClick(e, listing.id)}
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              /* List View */
              <div className="space-y-4 mb-8">
                {listings.map((listing, index) => (
                  <Card
                    key={listing.id}
                    className="group cursor-pointer bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:bg-white dark:hover:bg-slate-800 hover:shadow-lg transition-all duration-300"
                    onClick={() => handleCardClick(listing.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6 flex-1">
                          {/* Business Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
                                {listing.businessName}
                              </h3>
                              <StatusBadge status={listing.status} />
                            </div>
                            <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
                              <div className="flex items-center gap-1">
                                <Building2 className="h-4 w-4" />
                                <span>{listing.industry}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                <span>{listing.generalLocation}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>{getDaysOnMarket(listing.dateListed)} days</span>
                              </div>
                            </div>
                          </div>

                          {/* Price */}
                          <div className="text-right">
                            <div className="text-2xl font-bold text-slate-900 dark:text-white">
                              {formatCurrencyAbbreviated(listing.askingPrice)}
                            </div>
                            {listing.annualRevenue && (
                              <div className="text-sm text-slate-600 dark:text-slate-400">
                                Revenue: {formatCurrencyAbbreviated(listing.annualRevenue)}
                              </div>
                            )}
                          </div>

                          {/* Actions */}
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-2 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCardClick(listing.id);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="gap-2 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => handleEditClick(e, listing.id)}
                            >
                              <Edit className="h-4 w-4" />
                              Edit
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + Math.max(1, currentPage - 2);
                    if (page > totalPages) return null;

                    return (
                      <Button
                        key={page}
                        variant={page === currentPage ? "default" : "outline"}
                        onClick={() => handlePageChange(page)}
                        className={cn(
                          "w-10 h-10 p-0",
                          page === currentPage
                            ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white"
                            : "bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50"
                        )}
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50"
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default V2_Listings;
