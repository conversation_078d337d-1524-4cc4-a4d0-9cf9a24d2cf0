@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Rendyr brand colors in HSL */
    --charcoal: 0 0% 12%;       /* #1E1E1E */
    --bright-blue: 209 100% 50%; /* #0085FF */
    --red-orange: 12 100% 61%;  /* #FF5E3A */
    --warm-purple: 336 42% 51%; /* #B94B79 */
    --white: 0 0% 100%;         /* #FFFFFF */
    
    /* Applying brand colors to our theme */
    --background: 0 0% 100%;    /* White */
    --foreground: 0 0% 12%;     /* Charcoal */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 12%;

    --primary: 209 100% 50%;    /* Bright Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 336 42% 51%;   /* Warm Purple */
    --secondary-foreground: 0 0% 100%;

    --muted: 210 20% 96%;
    --muted-foreground: 0 0% 40%;

    /* --accent: 12 100% 61%;      Red Orange */
    /* --accent-foreground: 0 0% 100%; */

    --accent: 95 16% 65%;         /* Sage Green */
    --accent-foreground: 0 0% 12%;  /* Charcoal */

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 209 100% 50%;

    --radius: 0.5rem;

    /* Gradients - using brand colors */
    --gradient-primary: linear-gradient(135deg, hsl(12 100% 61%), hsl(336 42% 51%), hsl(209 100% 50%));
    --gradient-accent: linear-gradient(90deg, hsl(12 100% 61%), hsl(336 42% 51%));
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(30, 30, 30, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(30, 30, 30, 0.1), 0 2px 4px -1px rgba(30, 30, 30, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(30, 30, 30, 0.1), 0 4px 6px -2px rgba(30, 30, 30, 0.05);
    
    /* Animations */
    --transition-standard: all 0.3s ease;
  }
  

  .dark {
    /* Dark mode with Rendyr colors */
    --background: 0 0% 12%;      /* Charcoal as background */
    --foreground: 0 0% 100%;     /* White text */

    --card: 0 0% 15%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 100%;

    --primary: 209 100% 50%;     /* Bright Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 336 42% 51%;    /* Warm Purple */
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 80%;

    --accent: 12 100% 61%;       /* Red Orange */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 25%;
    --input: 0 0% 25%;
    --ring: 209 100% 50%;
    
    /* Gradients - using brand colors */
    --gradient-primary: linear-gradient(135deg, hsl(12 100% 61%), hsl(336 42% 51%), hsl(209 100% 50%));
    --gradient-accent: linear-gradient(90deg, hsl(12 100% 61%), hsl(336 42% 51%));
    
    /* Shadows - darker for dark mode */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }
}

@layer components {
  /* Beautiful Rendyr-inspired background texture */
  .texture-bg {
    background-color: hsl(var(--background));
    background-image: 
      radial-gradient(at 0% 0%, hsla(var(--red-orange), 0.08) 0px, transparent 50%),
      radial-gradient(at 100% 0%, hsla(var(--bright-blue), 0.08) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(var(--warm-purple), 0.08) 0px, transparent 50%),
      radial-gradient(at 100% 100%, hsla(var(--bright-blue), 0.05) 0px, transparent 50%);
  }

  .dark .texture-bg {
    background-image: 
      radial-gradient(at 0% 0%, hsla(var(--red-orange), 0.15) 0px, transparent 50%),
      radial-gradient(at 100% 0%, hsla(var(--bright-blue), 0.15) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(var(--warm-purple), 0.15) 0px, transparent 50%),
      radial-gradient(at 100% 100%, hsla(var(--bright-blue), 0.1) 0px, transparent 50%);
  }
  
  /* Rendyr-inspired UI components */
  .rendyr-card {
    @apply rounded-lg border bg-card p-6 shadow-md transition-all duration-300 hover:shadow-lg;
    background-image: linear-gradient(to bottom right, 
      rgba(255, 255, 255, 0.1), 
      rgba(255, 255, 255, 0.05) 30%, 
      rgba(0, 0, 0, 0.05));
  }
  
  .dark .rendyr-card {
    background-image: linear-gradient(to bottom right, 
      rgba(255, 255, 255, 0.08), 
      rgba(255, 255, 255, 0.03) 30%, 
      rgba(0, 0, 0, 0.1));
  }
  
  .rendyr-gradient-text {
    @apply font-bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
  
  .rendyr-btn {
    @apply relative overflow-hidden rounded-md transition-all duration-300;
  }
  
  .rendyr-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg, 
      transparent, 
      rgba(255, 255, 255, 0.2), 
      transparent
    );
    transition: 0.5s;
  }
  
  .rendyr-btn:hover::before {
    left: 100%;
  }
  
  /* Animations for elements */
  .fade-in-up {
    animation: fadeInUp 0.5s ease forwards;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}