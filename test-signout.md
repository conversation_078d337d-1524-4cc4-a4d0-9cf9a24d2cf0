# Sign Out Test Plan

## Expected Behavior
When a user clicks the sign out button:

1. The `signOut()` function should be called
2. The session data should be immediately set to null in the React Query cache
3. All cached data should be cleared
4. The SessionMonitor should detect the user state change from authenticated to null
5. The SessionMonitor should navigate to the index page (`/`)
6. The Index page should show the login form (not redirect back to dashboard)

## Key Changes Made

### 1. AuthContext signOut function
- Added `queryClient.setQueryData(['auth', 'session'], null)` before clearing cache
- This ensures the user state becomes null immediately

### 2. Sign out handlers (UserButton and AppSidebar)
- Removed manual navigation calls
- Let the SessionMonitor handle navigation automatically
- Only navigate manually in error cases

### 3. SessionMonitor
- Removed setTimeout delay
- Navigate immediately when user state changes from authenticated to null

### 4. Index page
- Simplified redirect logic
- Added loading state handling
- Use `replace: true` for navigation

### 5. Session Query
- Added `refetchOnWindowFocus: false` to prevent unwanted refetches

## Testing Steps
1. Sign in to the application
2. Navigate to any protected page (e.g., dashboard)
3. Click the sign out button (either in sidebar or user dropdown)
4. Verify you are redirected to the index page
5. Verify the login form is shown (not a redirect loop)