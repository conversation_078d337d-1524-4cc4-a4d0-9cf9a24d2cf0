import { test, expect, Page } from '@playwright/test';

interface LoadingOverlayLog {
  type: 'rendered' | 'removed';
  source: string;
  message?: string;
  overlayType: 'fullscreen' | 'inline';
  currentPath: string;
  timestamp: string;
  showMessage?: boolean;
}

/**
 * Test to capture and analyze LoadingOverlay usage during sign-out process
 */
test.describe('Sign-Out Loading Overlay Tracking', () => {
  let consoleLogs: any[] = [];
  let loadingOverlayLogs: LoadingOverlayLog[] = [];

  test.beforeEach(async ({ page }) => {
    // Clear logs before each test
    consoleLogs = [];
    loadingOverlayLogs = [];

    // Capture all console messages
    page.on('console', (msg) => {
      const text = msg.text();
      consoleLogs.push({
        type: msg.type(),
        text,
        timestamp: new Date().toISOString(),
      });

      // Parse LoadingOverlay specific logs
      if (text.includes('LoadingOverlay')) {
        try {
          if (text.includes('rendered:')) {
            const dataMatch = text.match(/LoadingOverlay \[(.*?)\] rendered: (.+)/);
            if (dataMatch) {
              const overlayType = dataMatch[1] as 'fullscreen' | 'inline';
              const data = JSON.parse(dataMatch[2]);
              loadingOverlayLogs.push({
                type: 'rendered',
                overlayType,
                ...data,
              });
            }
          } else if (text.includes('removed:')) {
            const dataMatch = text.match(/LoadingOverlay \[(.*?)\] removed: (.+)/);
            if (dataMatch) {
              const overlayType = dataMatch[1] as 'fullscreen' | 'inline';
              const data = JSON.parse(dataMatch[2]);
              loadingOverlayLogs.push({
                type: 'removed',
                overlayType,
                ...data,
              });
            }
          }
        } catch (e) {
          console.warn('Failed to parse LoadingOverlay log:', text);
        }
      }
    });
  });

  test('should track LoadingOverlay usage during sign-out process', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:8081');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Check if we're on login page or need to sign in
    const isOnLoginPage = await page.locator('text=Sign In').isVisible();
    
    if (isOnLoginPage) {
      console.log('⚠️  Already on login page - need to sign in first to test sign-out');
      
      // Try to sign in (this would need actual credentials)
      // For now, we'll just document that manual sign-in is needed
      await page.screenshot({ path: 'test-results/login-page.png' });
      
      test.skip('Manual sign-in required to test sign-out flow');
      return;
    }

    // If we're authenticated, proceed with sign-out test
    console.log('✅ User appears to be authenticated, proceeding with sign-out test');

    // Take screenshot before sign-out
    await page.screenshot({ path: 'test-results/before-signout.png' });

    // Clear existing logs to focus on sign-out process
    consoleLogs = [];
    loadingOverlayLogs = [];

    // Look for sign-out button (try multiple possible locations)
    const signOutSelectors = [
      'button:has-text("Sign out")',
      'button:has-text("Log out")',
      '[data-testid="sign-out-button"]',
      '[aria-label*="sign out" i]',
      '[aria-label*="log out" i]',
    ];

    let signOutButton = null;
    for (const selector of signOutSelectors) {
      const button = page.locator(selector).first();
      if (await button.isVisible()) {
        signOutButton = button;
        break;
      }
    }

    if (!signOutButton) {
      // Try to open user menu first
      const userMenuSelectors = [
        '[data-testid="user-menu"]',
        'button[aria-label*="user" i]',
        '.user-button',
        '[role="button"]:has([data-testid="avatar"])',
      ];

      for (const selector of userMenuSelectors) {
        const menu = page.locator(selector).first();
        if (await menu.isVisible()) {
          await menu.click();
          await page.waitForTimeout(500); // Wait for menu to open
          
          // Try to find sign-out button in the opened menu
          for (const signOutSelector of signOutSelectors) {
            const button = page.locator(signOutSelector).first();
            if (await button.isVisible()) {
              signOutButton = button;
              break;
            }
          }
          break;
        }
      }
    }

    if (!signOutButton) {
      await page.screenshot({ path: 'test-results/no-signout-button.png' });
      throw new Error('Could not find sign-out button');
    }

    console.log('🎯 Found sign-out button, clicking...');

    // Click sign-out button
    await signOutButton.click();

    // Wait for sign-out process to complete
    await page.waitForTimeout(2000);

    // Take screenshot after sign-out
    await page.screenshot({ path: 'test-results/after-signout.png' });

    // Wait a bit more to capture any delayed loading states
    await page.waitForTimeout(1000);

    // Analyze the captured logs
    console.log('\n📊 SIGN-OUT LOADING OVERLAY ANALYSIS');
    console.log('=====================================');

    console.log(`\n📝 Total console logs captured: ${consoleLogs.length}`);
    console.log(`🔄 LoadingOverlay events captured: ${loadingOverlayLogs.length}`);

    if (loadingOverlayLogs.length > 0) {
      console.log('\n🔍 LoadingOverlay Events During Sign-Out:');
      loadingOverlayLogs.forEach((log, index) => {
        console.log(`\n${index + 1}. ${log.type.toUpperCase()} - ${log.overlayType}`);
        console.log(`   Source: ${log.source}`);
        console.log(`   Path: ${log.currentPath}`);
        console.log(`   Message: ${log.message || 'N/A'}`);
        console.log(`   Timestamp: ${log.timestamp}`);
      });

      // Check for problematic loading overlays
      const fullscreenOverlays = loadingOverlayLogs.filter(log => 
        log.overlayType === 'fullscreen' && log.type === 'rendered'
      );

      if (fullscreenOverlays.length > 0) {
        console.log('\n⚠️  FULLSCREEN LOADING OVERLAYS DETECTED:');
        fullscreenOverlays.forEach(log => {
          console.log(`   - ${log.source}: ${log.message}`);
        });
      } else {
        console.log('\n✅ No fullscreen loading overlays detected during sign-out');
      }

      // Check for auth-related loading overlays
      const authOverlays = loadingOverlayLogs.filter(log => 
        log.source.toLowerCase().includes('auth') || 
        log.source.toLowerCase().includes('protected')
      );

      if (authOverlays.length > 0) {
        console.log('\n🔐 AUTH-RELATED LOADING OVERLAYS:');
        authOverlays.forEach(log => {
          console.log(`   - ${log.source}: ${log.message} (${log.type})`);
        });
      }

    } else {
      console.log('\n✅ No LoadingOverlay events captured during sign-out process');
    }

    // Check final state
    const finalUrl = page.url();
    const isOnLoginPageAfter = finalUrl.includes('/') && !finalUrl.includes('/dashboard');
    
    console.log(`\n🎯 Final URL: ${finalUrl}`);
    console.log(`✅ Successfully navigated to login page: ${isOnLoginPageAfter}`);

    // Save detailed log report
    const report = {
      testTimestamp: new Date().toISOString(),
      finalUrl,
      successfulSignOut: isOnLoginPageAfter,
      totalConsoleLogs: consoleLogs.length,
      loadingOverlayEvents: loadingOverlayLogs.length,
      fullscreenOverlays: loadingOverlayLogs.filter(log => log.overlayType === 'fullscreen'),
      authRelatedOverlays: loadingOverlayLogs.filter(log => 
        log.source.toLowerCase().includes('auth') || 
        log.source.toLowerCase().includes('protected')
      ),
      allLoadingOverlayLogs: loadingOverlayLogs,
      allConsoleLogs: consoleLogs,
    };

    // Write report to file
    await page.evaluate((reportData) => {
      console.log('📄 DETAILED SIGN-OUT REPORT:', JSON.stringify(reportData, null, 2));
    }, report);

    // Assertions
    expect(isOnLoginPageAfter).toBe(true);
    expect(loadingOverlayLogs.filter(log => 
      log.overlayType === 'fullscreen' && log.type === 'rendered'
    )).toHaveLength(0); // No fullscreen overlays should appear during sign-out
  });

  test.afterEach(async ({ page }) => {
    // Clean up and save final report
    console.log('\n📋 Test completed. Check screenshots in test-results/ directory');
  });
});
