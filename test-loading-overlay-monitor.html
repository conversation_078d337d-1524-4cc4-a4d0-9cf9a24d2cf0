<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoadingOverlay Monitor - Sign-Out Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .log-monitor {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-loading-rendered {
            color: #ffa500;
            font-weight: bold;
        }
        .log-loading-removed {
            color: #90ee90;
        }
        .log-auth {
            color: #87ceeb;
        }
        .log-error {
            color: #ff6b6b;
        }
        .log-warning {
            color: #ffd93d;
        }
        .controls {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🔄 LoadingOverlay Monitor - Sign-Out Testing</h1>
    
    <div class="test-section success">
        <h2>✅ Comprehensive Logging Implemented</h2>
        <p>All LoadingOverlay components now include detailed logging with source tracking. This page monitors console logs in real-time to identify any loading overlays that appear during sign-out.</p>
    </div>

    <div class="test-section">
        <h2>📊 Real-Time Statistics</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-logs">0</div>
                <div class="stat-label">Total Console Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="loading-events">0</div>
                <div class="stat-label">LoadingOverlay Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="fullscreen-overlays">0</div>
                <div class="stat-label">Fullscreen Overlays</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="auth-overlays">0</div>
                <div class="stat-label">Auth-Related Overlays</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎮 Test Controls</h2>
        <div class="controls">
            <button class="btn" onclick="clearLogs()">Clear Logs</button>
            <button class="btn" onclick="exportLogs()">Export Logs</button>
            <button class="btn" onclick="toggleAutoScroll()">Toggle Auto-Scroll</button>
            <button class="btn btn-danger" onclick="openApp()">Open App (New Tab)</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 Console Log Monitor</h2>
        <div class="log-monitor" id="log-monitor">
            <div class="log-entry">🔍 Monitoring console logs... Open the app and perform sign-out to see LoadingOverlay tracking.</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Testing Instructions</h2>
        <ol>
            <li><strong>Open the app:</strong> Click "Open App" button above or navigate to <a href="http://localhost:8081" target="_blank">http://localhost:8081</a></li>
            <li><strong>Sign in</strong> to the application if not already authenticated</li>
            <li><strong>Keep this monitor page open</strong> in a separate tab/window</li>
            <li><strong>Perform sign-out</strong> in the app using any sign-out button</li>
            <li><strong>Watch the logs</strong> in real-time to see which LoadingOverlay components are triggered</li>
            <li><strong>Analyze results:</strong> Look for any fullscreen overlays or unexpected loading states</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 What to Look For</h2>
        <ul>
            <li><strong class="log-loading-rendered">🔄 LoadingOverlay rendered</strong> - When a loading overlay appears</li>
            <li><strong class="log-loading-removed">✅ LoadingOverlay removed</strong> - When a loading overlay disappears</li>
            <li><strong>Source identifiers</strong> - Which component triggered the overlay (e.g., "ProtectedRoute:Dashboard:AuthLoading")</li>
            <li><strong>Fullscreen vs Inline</strong> - Type of overlay (fullscreen overlays are more disruptive)</li>
            <li><strong>Timing</strong> - When overlays appear relative to sign-out action</li>
        </ul>
    </div>

    <div class="test-section warning">
        <h2>⚠️ Expected Results</h2>
        <p><strong>With our recent fixes, you should see:</strong></p>
        <ul>
            <li>✅ <strong>No fullscreen loading overlays</strong> during sign-out process</li>
            <li>✅ <strong>Immediate navigation</strong> to login page without delays</li>
            <li>✅ <strong>No ProtectedRoute loading states</strong> interfering with sign-out</li>
            <li>✅ <strong>Clean console logs</strong> showing successful sign-out flow</li>
        </ul>
        <p><strong>If you see unexpected loading overlays, this indicates areas that need further investigation.</strong></p>
    </div>

    <script>
        let logs = [];
        let loadingOverlayEvents = [];
        let autoScroll = true;
        let stats = {
            totalLogs: 0,
            loadingEvents: 0,
            fullscreenOverlays: 0,
            authOverlays: 0
        };

        // Override console methods to capture logs
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info
        };

        function captureConsoleLog(type, args) {
            const timestamp = new Date().toISOString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');

            const logEntry = {
                type,
                message,
                timestamp,
                args
            };

            logs.push(logEntry);
            stats.totalLogs++;

            // Check if this is a LoadingOverlay log
            if (message.includes('LoadingOverlay')) {
                stats.loadingEvents++;
                
                if (message.includes('fullscreen')) {
                    stats.fullscreenOverlays++;
                }
                
                if (message.toLowerCase().includes('auth') || 
                    message.toLowerCase().includes('protected')) {
                    stats.authOverlays++;
                }

                loadingOverlayEvents.push(logEntry);
            }

            updateDisplay(logEntry);
            updateStats();

            // Call original console method
            originalConsole[type].apply(console, args);
        }

        // Override console methods
        console.log = (...args) => captureConsoleLog('log', args);
        console.warn = (...args) => captureConsoleLog('warn', args);
        console.error = (...args) => captureConsoleLog('error', args);
        console.info = (...args) => captureConsoleLog('info', args);

        function updateDisplay(logEntry) {
            const monitor = document.getElementById('log-monitor');
            const logDiv = document.createElement('div');
            logDiv.className = 'log-entry';

            // Apply styling based on log content
            if (logEntry.message.includes('LoadingOverlay') && logEntry.message.includes('rendered')) {
                logDiv.className += ' log-loading-rendered';
            } else if (logEntry.message.includes('LoadingOverlay') && logEntry.message.includes('removed')) {
                logDiv.className += ' log-loading-removed';
            } else if (logEntry.message.toLowerCase().includes('auth')) {
                logDiv.className += ' log-auth';
            } else if (logEntry.type === 'error') {
                logDiv.className += ' log-error';
            } else if (logEntry.type === 'warn') {
                logDiv.className += ' log-warning';
            }

            const time = new Date(logEntry.timestamp).toLocaleTimeString();
            logDiv.textContent = `[${time}] ${logEntry.type.toUpperCase()}: ${logEntry.message}`;

            monitor.appendChild(logDiv);

            // Auto-scroll to bottom
            if (autoScroll) {
                monitor.scrollTop = monitor.scrollHeight;
            }

            // Limit log entries to prevent memory issues
            if (monitor.children.length > 1000) {
                monitor.removeChild(monitor.firstChild);
            }
        }

        function updateStats() {
            document.getElementById('total-logs').textContent = stats.totalLogs;
            document.getElementById('loading-events').textContent = stats.loadingEvents;
            document.getElementById('fullscreen-overlays').textContent = stats.fullscreenOverlays;
            document.getElementById('auth-overlays').textContent = stats.authOverlays;
        }

        function clearLogs() {
            logs = [];
            loadingOverlayEvents = [];
            stats = {
                totalLogs: 0,
                loadingEvents: 0,
                fullscreenOverlays: 0,
                authOverlays: 0
            };
            
            const monitor = document.getElementById('log-monitor');
            monitor.innerHTML = '<div class="log-entry">🔍 Logs cleared. Monitoring console logs...</div>';
            
            updateStats();
        }

        function exportLogs() {
            const report = {
                timestamp: new Date().toISOString(),
                stats,
                loadingOverlayEvents,
                allLogs: logs
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `signout-loading-overlay-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const btn = event.target;
            btn.textContent = autoScroll ? 'Toggle Auto-Scroll' : 'Auto-Scroll: OFF';
            btn.style.background = autoScroll ? '#007bff' : '#6c757d';
        }

        function openApp() {
            window.open('http://localhost:8081', '_blank');
        }

        // Initialize
        updateStats();
        
        // Add a test log to verify monitoring is working
        setTimeout(() => {
            console.log('🔍 LoadingOverlay Monitor initialized and ready for testing');
        }, 1000);
    </script>
</body>
</html>
